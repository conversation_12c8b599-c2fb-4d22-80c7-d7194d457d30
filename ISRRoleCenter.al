page 50140 "ISR Role Center"
{
    Caption = 'ISR Role Center';
    PageType = RoleCenter;
    ApplicationArea = All;

    layout
    {
        area(RoleCenter)
        {
            part("OpportunityKPIs"; "ISR Opportunity KPI")
            {
                ApplicationArea = All;
            }
            part("OpportunityList"; "ISR Opportunity List")
            {
                ApplicationArea = All;
                Caption = 'My Recent Opportunities';
            }
        }
    }

    actions
    {
        area(Sections)
        {
            group("Opportunities")
            {
                Caption = 'Opportunities';
                Image = Sales;
                action("All Opportunities")
                {
                    ApplicationArea = All;
                    Caption = 'All Opportunities';
                    Image = Opportunity;
                    RunObject = page "Opportunity List";
                    ToolTip = 'View all opportunities';
                }
                action("ISR Opportunities")
                {
                    ApplicationArea = All;
                    Caption = 'My Opportunities';
                    Image = Opportunity;
                    RunObject = page "ISR Opportunity List Page";
                    ToolTip = 'View my assigned opportunities';
                }
                action("My Open Opportunities")
                {
                    ApplicationArea = All;
                    Caption = 'My Open Opportunities';
                    Image = Opportunity;
                    RunObject = page "ISR Open Opportunity List";
                    ToolTip = 'Quick access to my open opportunities';
                }
            }
            group("Contacts & Customers")
            {
                Caption = 'Contacts & Customers';
                Image = RegisteredDocs;
                action("Contacts")
                {
                    ApplicationArea = All;
                    Caption = 'Contacts';
                    Image = ContactPerson;
                    RunObject = page "Contact List";
                    ToolTip = 'View and manage contacts';
                }
                action("My Contacts")
                {
                    ApplicationArea = All;
                    Caption = 'My Contacts';
                    Image = ContactPerson;
                    RunObject = page "ISR My Contacts List";
                    ToolTip = 'View and manage my contacts';
                }
                action("Customers")
                {
                    ApplicationArea = All;
                    Caption = 'Customers';
                    Image = Customer;
                    RunObject = page "Customer List";
                    ToolTip = 'View and manage customers';
                }
            }
        }
        area(Embedding)
        {
        }
        area(Processing)
        {
            action("Opportunity Reports")
            {
                ApplicationArea = All;
                Caption = 'Opportunity Reports';
                Image = Report;
                ToolTip = 'View opportunity reports';
            }
        }
    }
}