// Page to view and manage mass email jobs
page 50113 "Mass Email Jobs"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    Caption = 'Mass Email Jobs';
    SourceTable = "Mass Email Queue";
    CardPageId = "Mass Email Job Card";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(Jobs)
            {
                field("Job ID"; Rec."Job ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Unique identifier for the mass email job.';
                }
                field("Job Status"; Rec."Job Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Current status of the mass email job.';
                    StyleExpr = StatusStyleExpr;
                }
                field("Progress Percentage"; Rec."Progress Percentage")
                {
                    ApplicationArea = All;
                    ToolTip = 'Percentage of emails processed (sent + failed).';
                    StyleExpr = ProgressStyleExpr;
                }
                field("Created Date Time"; Rec."Created Date Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Date and time when the job was created.';
                }
                field("Created By"; Rec."Created By")
                {
                    ApplicationArea = All;
                    ToolTip = 'User who created the job.';
                }
                field("Email Subject"; Rec."Email Subject")
                {
                    ApplicationArea = All;
                    ToolTip = 'Subject line of the emails being sent.';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Salesperson associated with the email campaign.';
                }
                field("Campaign No."; Rec."Campaign No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Campaign associated with the email job.';
                }
                field("Total Recipients"; Rec."Total Recipients")
                {
                    ApplicationArea = All;
                    ToolTip = 'Total number of recipients for this job.';
                }
                field("Emails Sent"; Rec."Emails Sent")
                {
                    ApplicationArea = All;
                    ToolTip = 'Number of emails successfully sent.';
                    Style = Favorable;
                }
                field("Emails Failed"; Rec."Emails Failed")
                {
                    ApplicationArea = All;
                    ToolTip = 'Number of emails that failed to send.';
                    Style = Unfavorable;
                }
                field("Started Date Time"; Rec."Started Date Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Date and time when the job started processing.';
                }
                field("Completed Date Time"; Rec."Completed Date Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Date and time when the job completed.';
                }
                field("Current Batch"; Rec."Current Batch")
                {
                    ApplicationArea = All;
                    ToolTip = 'Current batch being processed.';
                }
                field("Last Error Message"; Rec."Last Error Message")
                {
                    ApplicationArea = All;
                    ToolTip = 'Last error message encountered during processing.';
                    Style = Unfavorable;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ViewRecipients)
            {
                ApplicationArea = All;
                Caption = 'View Recipients';
                Image = ContactPerson;
                ToolTip = 'View the recipients for this mass email job and their send status.';

                trigger OnAction()
                begin
                    ViewJobRecipients();
                end;
            }

            action(ViewLogs)
            {
                ApplicationArea = All;
                Caption = 'View Logs';
                Image = Log;
                ToolTip = 'View detailed logs for this mass email job.';

                trigger OnAction()
                begin
                    ViewJobLogs();
                end;
            }

            action(RefreshStatus)
            {
                ApplicationArea = All;
                Caption = 'Refresh';
                Image = Refresh;
                ToolTip = 'Refresh the job status and progress information.';

                trigger OnAction()
                begin
                    CurrPage.Update(false);
                end;
            }

            action(CancelJob)
            {
                ApplicationArea = All;
                Caption = 'Cancel Job';
                Image = Cancel;
                ToolTip = 'Cancel the selected job if it is still pending or in progress.';
                Enabled = CanCancelJob;

                trigger OnAction()
                begin
                    CancelSelectedJob();
                end;
            }
        }

        area(Promoted)
        {
            group(JobActions)
            {
                Caption = 'Job Actions';
                actionref(ViewRecipientsRef; ViewRecipients)
                {
                }
                actionref(ViewLogsRef; ViewLogs)
                {
                }
                actionref(RefreshStatusRef; RefreshStatus)
                {
                }
                actionref(CancelJobRef; CancelJob)
                {
                }
            }
        }
    }

    var
        StatusStyleExpr: Text;
        ProgressStyleExpr: Text;
        CanCancelJob: Boolean;

    trigger OnAfterGetRecord()
    begin
        SetStyleExpressions();
        SetActionVisibility();
    end;

    local procedure SetStyleExpressions()
    begin
        case Rec."Job Status" of
            Rec."Job Status"::Pending:
                StatusStyleExpr := 'Standard';
            Rec."Job Status"::"In Progress":
                StatusStyleExpr := 'StandardAccent';
            Rec."Job Status"::Completed:
                StatusStyleExpr := 'Favorable';
            Rec."Job Status"::Failed:
                StatusStyleExpr := 'Unfavorable';
            Rec."Job Status"::Cancelled:
                StatusStyleExpr := 'Subordinate';
            else
                StatusStyleExpr := 'Standard';
        end;

        if Rec."Progress Percentage" >= 100 then
            ProgressStyleExpr := 'Favorable'
        else if Rec."Progress Percentage" >= 50 then
            ProgressStyleExpr := 'StandardAccent'
        else
            ProgressStyleExpr := 'Standard';
    end;

    local procedure SetActionVisibility()
    begin
        CanCancelJob := Rec."Job Status" in [Rec."Job Status"::Pending, Rec."Job Status"::"In Progress"];
    end;

    local procedure ViewJobRecipients()
    var
        MassEmailRecipients: Record "Mass Email Recipients";
        MassEmailRecipientsPage: Page "Mass Email Recipients";
    begin
        MassEmailRecipients.SetRange("Job ID", Rec."Job ID");
        MassEmailRecipientsPage.SetTableView(MassEmailRecipients);
        MassEmailRecipientsPage.Run();
    end;

    local procedure ViewJobLogs()
    var
        MassEmailLog: Record "Mass Email Log";
        MassEmailLogsPage: Page "Mass Email Logs";
    begin
        MassEmailLog.SetRange("Job ID", Rec."Job ID");
        MassEmailLogsPage.SetTableView(MassEmailLog);
        MassEmailLogsPage.Run();
    end;

    local procedure CancelSelectedJob()
    var
        JobQueueEntry: Record "Job Queue Entry";
    begin
        if not Confirm('Are you sure you want to cancel job %1?\\This will stop the job from processing any remaining emails.', false, Rec."Job ID") then
            exit;

        // Update job status
        Rec."Job Status" := Rec."Job Status"::Cancelled;
        Rec."Completed Date Time" := CurrentDateTime();
        Rec.Modify();

        // Cancel the job queue entry if it exists
        if not IsNullGuid(Rec."Job Queue Entry ID") then begin
            if JobQueueEntry.Get(Rec."Job Queue Entry ID") then begin
                JobQueueEntry.SetStatus(JobQueueEntry.Status::"On Hold");
                JobQueueEntry.Modify();
            end;
        end;

        Message('Job %1 has been cancelled.', Rec."Job ID");
        CurrPage.Update(false);
    end;
}
