// Table to store recipients for each mass email job
table 50113 "Mass Email Recipients"
{
    Caption = 'Mass Email Recipients';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Job ID"; Code[20])
        {
            Caption = 'Job ID';
            DataClassification = CustomerContent;
            TableRelation = "Mass Email Queue"."Job ID";
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            DataClassification = CustomerContent;
        }
        field(3; "Contact No."; Code[20])
        {
            Caption = 'Contact No.';
            DataClassification = CustomerContent;
            TableRelation = Contact;
        }
        field(4; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
            DataClassification = CustomerContent;
        }
        field(5; "E-Mail"; Text[80])
        {
            Caption = 'E-Mail';
            DataClassification = CustomerContent;
        }
        field(6; "Company Name"; Text[100])
        {
            Caption = 'Company Name';
            DataClassification = CustomerContent;
        }
        field(7; "Salesperson Code"; Code[20])
        {
            Caption = 'Salesperson Code';
            DataClassification = CustomerContent;
            TableRelation = "Salesperson/Purchaser";
        }
        field(8; "Send Status"; Enum "Mass Email Send Status")
        {
            Caption = 'Send Status';
            DataClassification = CustomerContent;
        }
        field(9; "Sent Date Time"; DateTime)
        {
            Caption = 'Sent Date Time';
            DataClassification = CustomerContent;
        }
        field(10; "Error Message"; Text[250])
        {
            Caption = 'Error Message';
            DataClassification = CustomerContent;
        }
        field(11; "Personalized Subject"; Text[250])
        {
            Caption = 'Personalized Subject';
            DataClassification = CustomerContent;
        }
        field(12; "Retry Count"; Integer)
        {
            Caption = 'Retry Count';
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Job ID", "Line No.")
        {
            Clustered = true;
        }
        key(Status; "Job ID", "Send Status")
        {
        }
        key(Contact; "Contact No.")
        {
        }
    }

    trigger OnInsert()
    begin
        if "Send Status" = "Send Status"::" " then
            "Send Status" := "Send Status"::Pending;
    end;
}
