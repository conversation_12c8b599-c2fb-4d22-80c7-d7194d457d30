page 50114 "Updated Opportunity List"
{
    PageType = ListPart;
    SourceTable = "Updated Opportunity";
    SourceTableTemporary = true;
    Caption = 'Updated Opportunities';
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            repeater(Group)
            {
                field(OpportunityNo; Rec."Opportunity No.")
                {
                    ApplicationArea = All;

                    trigger OnDrillDown()
                    var
                        OpportunityRec: Record Opportunity;
                    begin
                        OpportunityRec.Reset();
                        OpportunityRec.SetRange("No.", Rec."Opportunity No.");
                        if OpportunityRec.FindFirst() then
                            Page.Run(Page::"Opportunity Card", OpportunityRec);
                    end;
                }
                field(OpportunityName; Rec."Opportunity Name")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action(OpenOpportunity)
            {
                Caption = 'Open Opportunity';
                ApplicationArea = All;
                Image = EditLines;

                trigger OnAction()
                var
                    OpportunityRec: Record Opportunity;
                begin
                    OpportunityRec.Reset();
                    OpportunityRec.SetRange("No.", Rec."Opportunity No.");
                    if OpportunityRec.FindFirst() then
                        Page.Run(Page::"Opportunity Card", OpportunityRec);
                end;
            }
        }
    }

    // Public procedure to load data from the parent page
    procedure SetSource(var TempUpdatedOpportunities: Record "Updated Opportunity")
    begin
        // Clear current data
        Rec.Reset();
        Rec.DeleteAll();

        // Copy records from the supplied temporary table
        if TempUpdatedOpportunities.FindSet() then
            repeat
                Rec := TempUpdatedOpportunities;
                Rec.Insert();
            until TempUpdatedOpportunities.Next() = 0;

        CurrPage.Update(false);
    end;
}