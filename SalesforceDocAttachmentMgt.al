codeunit 50138 "Salesforce Doc Attachment Mgt."
{
    [EventSubscriber(ObjectType::Page, Page::"Doc. Attachment List Factbox", 'OnAfterGetRecRefFail', '', false, false)]
    local procedure OnAfterGetRecRefFail(DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef);
    var
        TempDocImport: Record "Temp Document Import";
    begin
        case DocumentAttachment."Table ID" of
            DATABASE::"Temp Document Import":
                begin
                    RecRef.Open(DATABASE::"Temp Document Import");
                    if TempDocImport.Get(DocumentAttachment."No.") then
                        RecRef.GetTable(TempDocImport);
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Page, Page::"Document Attachment Details", 'OnAfterOpenForRecRef', '', false, false)]
    local procedure OnAfterOpenForRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef; var FlowFieldsEditable: Boolean);
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
    begin
        case RecRef.Number of
            DATABASE::"Temp Document Import":
                begin
                    // For our temporary document import table, we use a fixed value of '0'
                    DocumentAttachment.SetRange("No.", '0');
                    FlowFieldsEditable := false;
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Document Attachment", 'OnAfterInitFieldsFromRecRef', '', false, false)]
    local procedure OnAfterInitFieldsFromRecRef(var DocumentAttachment: Record "Document Attachment"; var RecRef: RecordRef)
    var
        FieldRef: FieldRef;
        RecNo: Code[20];
    begin
        case RecRef.Number of
            DATABASE::"Temp Document Import":
                begin
                    // For our temporary document import table, we use a fixed value of '0'
                    DocumentAttachment.Validate("No.", '0');
                end;
        end;
    end;
}