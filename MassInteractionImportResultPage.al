page 50147 "Mass Interaction Import Result"
{
    PageType = List;
    SourceTable = "Mass Interaction Import Result";
    SourceTableTemporary = true;
    ApplicationArea = All;
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(content)
        {
            repeater(Group)
            {
                field("Row No."; Rec."Row No.") { ApplicationArea = All; }
                field("Contact Identifier"; Rec."Contact Identifier") { ApplicationArea = All; }
                field("Contact No."; Rec."Contact No.") { ApplicationArea = All; }
                field("Contact Email"; Rec."Contact Email") { ApplicationArea = All; }
                field("Interaction Code"; Rec."Interaction Code") { ApplicationArea = All; }
                field("Campaign No."; Rec."Campaign No.") { ApplicationArea = All; }
                field(Comment; Rec.Comment) { ApplicationArea = All; }
                field(Status; Rec.Status) { ApplicationArea = All; }
                field(Message; Rec.Message) { ApplicationArea = All; }
            }
        }
    }
}