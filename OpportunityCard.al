namespace DefaultPublisher.ALProject1;

using Microsoft.Sales.Customer;
using Microsoft.CRM.Opportunity;
using System.Text;
using System.Reflection;
using Microsoft.CRM.BusinessRelation;
using Microsoft.CRM.Campaign;

tableextension 50100 "MyOpportunityExt" extends "Opportunity"
{
    fields
    {
        field(50100; "Amount"; Decimal)
        {
            Caption = 'Amount (Selling Price)';
            trigger OnValidate()
            begin
                CalculateNetMargin();
                CalculateMargins();
            end;
        }
        field(50101; "Cost__c"; Decimal)
        {
            Caption = 'Cost';
            trigger OnValidate()
            begin
                CalculateNetMargin();
                CalculateMargins();
            end;
        }

        field(50103; "Margin_Percent__c"; Decimal)
        {
            Caption = 'Margin on the sale (%)';
        }
        field(50104; "Type_of_Project__c"; Option)
        {
            Caption = 'Type of Opportunity';
            OptionMembers = "None","Hardware with support","Hardware without support","Hardware + services","Service","Managed Service","Software","Tender Offer";
            trigger OnValidate()
            begin
                CalculateNetMargin();
            end;
        }
        field(50134; "Type_of_Project_Multi__c"; Text[250])
        {
            Caption = 'Type of Opportunity *';
            Editable = false;
            trigger OnValidate()
            begin
                ValidateOpportunityTypes();
                CalculateNetMargin();
            end;
        }
        field(50132; "Type_of_Technology__c"; Text[250])
        {
            Caption = 'Type of Technology';
            Editable = false;
            trigger OnValidate()
            begin
                ValidateTechnologyTypes();
            end;
        }
        field(50133; "Type_Of_Support_Multi__c"; Text[250])
        {
            Caption = 'Type of Support *';
            Editable = false;
            trigger OnValidate()
            begin
                ValidateSupportTypes();
            end;
        }
        field(50105; "Type_Of_Support__c"; Option)
        {
            Caption = 'Type of Support';
            OptionMembers = "None","Installation","Renewal","New license","New Managed Service","Other";

        }
        field(50106; "Type_Of_Service__c"; Option)
        {
            Caption = 'Type of Service';
            OptionMembers = "None","Prepaid","Project";
        }
        field(50107; "Type_Of_License__c"; Option)
        {
            Caption = 'Type of License';
            OptionMembers = "None","MSP","Non-MSP";
        }
        field(50108; "PnL_Total_Hours__c"; Decimal)
        {
            Caption = 'Total PnL Hours';
        }
        field(50109; "Partner_For_The_Project__c"; Text[255])
        {
            Caption = 'Partner';
            TableRelation = "Contact Business Relation"."Contact Name" WHERE("Business Relation Code" = CONST('PARTNER'));
            ValidateTableRelation = true;
            trigger OnLookup()
            var
                ContactBusRel: Record "Contact Business Relation";
            begin
                ContactBusRel.SetRange("Business Relation Code", 'PARTNER');
                if Page.RunModal(Page::"Contact Business Relations", ContactBusRel) = Action::LookupOK then
                    Rec."Partner_For_The_Project__c" := ContactBusRel."Contact Name";
            end;
        }
        field(50110; "Manufacturer__c"; Option)
        {
            Caption = 'Distributor';
            OptionMembers = "Autres","Arrow","Dell","Exclusive Network","Ingrammicro","Prival","Securitas","TDSynnex";
        }
        field(50111; "Serial_Number__c"; Text[2048])
        {
            Caption = 'Serial Number';
            OptimizeForTextSearch = true;
            trigger OnValidate()
            begin
                ValidateSerialNumber();
            end;
        }
        field(50112; "Support_Start_Date__c"; Date)
        {
            Caption = 'Support Start Date';
            trigger OnValidate()
            begin
                ValidateSerialNumber();
            end;
        }
        field(50113; "Support_End_Date__c"; Date)
        {
            Caption = 'Support End Date';
            trigger OnValidate()
            begin
                ValidateSerialNumber();
            end;
        }
        field(50114; "Renewal_Date__c"; Date)
        {
            Caption = 'Renewal Date';
        }
        field(50115; "Fiscal_Week__c"; Integer)
        {
            Caption = 'Fiscal Week';
        }
        field(50116; "Fiscal_Year_Start__c"; Date)
        {
            Caption = 'Fiscal Year Start';
        }
        field(50117; "Fiscal_Year_End__c"; Date)
        {
            Caption = 'Fiscal Year End';
        }
        field(50118; "Tender_Offer_ID__c"; Text[255])
        {
            Caption = 'Tender Offer ID';
        }
        field(50119; "Technician_Group__c"; Option)
        {
            Caption = 'Technician Group';
            OptionMembers = "None","Tech Network","Tech System","Tech Monitoring","Tech Cybersecurity","Tech RTLS";
        }
        field(50120; "Technician_Assigned__c"; Text[255])
        {
            Caption = 'Technician Assigned';
        }
        field(50121; "Internal_Project_Name__c"; Text[255])
        {
            Caption = 'Internal Project Name';
        }
        field(50122; "Follow_Up_Reminder__c"; Date)
        {
            Caption = 'Follow Up Reminder';
        }
        field(50123; "ERP_Quote_Number__c"; Text[255])
        {
            Caption = 'Quote Number';
            OptimizeForTextSearch = true;
        }
        field(50124; "Partner_Quote_Number__c"; Text[255])
        {
            Caption = 'Partner Quote Number';
            OptimizeForTextSearch = true;
        }
        field(50125; "Client_PO_Number__c"; Text[255])
        {
            Caption = 'Client PO Number';
            OptimizeForTextSearch = true;
        }
        field(50126; "Internal_PO_Number__c"; Text[255])
        {
            Caption = 'Internal PO Number';
            OptimizeForTextSearch = true;
        }
        field(50127; "Invoice__c"; Text[255])
        {
            Caption = 'Invoice';
            OptimizeForTextSearch = true;
        }
        field(50128; "SalesforceOppId"; Text[255])
        {
            Caption = 'Salesforce Opportunity ID';
        }
        field(50129; "SalesforceOppDesc"; BLOB)
        {
            Caption = 'Salesforce Description';
        }
        field(50131; "CloseDate"; Date)
        {
            Caption = 'Close Date';
        }
        field(50135; "EOS"; Boolean)
        {
            Caption = 'EOS';
        }
        field(50136; "Estimated Delivery Date"; Date)
        {
            Caption = 'Estimated Delivery Date';
        }
        field(50137; "Campaign"; Code[20])
        {
            Caption = 'Campaign';
            TableRelation = "Campaign"."No.";
        }
        field(50138; "Net_Margin__c"; Decimal)
        {
            Caption = 'Net Margin';
        }
        field(50139; "Delivered"; Boolean)
        {
            Caption = 'Delivered';
        }
    }





    local procedure ValidateTechnologyTypes()
    var
        TechnologyType: Text;
        TechnologyTypes: List of [Text];
        ValidTypes: List of [Text];
    begin
        if "Type_of_Technology__c" = '' then
            exit;

        ValidTypes.Add('Cyber Security');
        ValidTypes.Add('Network');
        ValidTypes.Add('Monitoring');
        ValidTypes.Add('RTLS');
        ValidTypes.Add('System Infra');
        ValidTypes.Add('Other');

        TechnologyTypes := "Type_of_Technology__c".Split(',');
        foreach TechnologyType in TechnologyTypes do begin
            TechnologyType := DelChr(TechnologyType, '<>', ' ');
            if not ValidTypes.Contains(TechnologyType) then
                Error('Invalid technology type: %1. Valid types are: Cyber Security, Network, Monitoring, RTLS, System Infra, Other', TechnologyType);
        end;
    end;

    local procedure ValidateSupportTypes()
    var
        SupportType: Text;
        SupportTypes: List of [Text];
        ValidTypes: List of [Text];
    begin
        if "Type_Of_Support_Multi__c" = '' then
            exit;

        ValidTypes.Add('Installation');
        ValidTypes.Add('Renewal');
        ValidTypes.Add('New license');
        ValidTypes.Add('New Managed Service');
        ValidTypes.Add('Other');

        SupportTypes := "Type_Of_Support_Multi__c".Split(',');
        foreach SupportType in SupportTypes do begin
            SupportType := DelChr(SupportType, '<>', ' ');
            if not ValidTypes.Contains(SupportType) then
                Error('Invalid support type: %1. Valid types are: Installation, Renewal, New license, New Managed Service, Other', SupportType);
        end;
    end;

    local procedure ValidateOpportunityTypes()
    var
        OpportunityType: Text;
        OpportunityTypes: List of [Text];
        ValidTypes: List of [Text];
    begin
        if "Type_of_Project_Multi__c" = '' then
            exit;

        ValidTypes.Add('Hardware with support');
        ValidTypes.Add('Hardware without support');
        ValidTypes.Add('Hardware + services');
        ValidTypes.Add('Service');
        ValidTypes.Add('Managed Service');
        ValidTypes.Add('Software');
        ValidTypes.Add('Tender Offer');

        OpportunityTypes := "Type_of_Project_Multi__c".Split(',');
        foreach OpportunityType in OpportunityTypes do begin
            OpportunityType := DelChr(OpportunityType, '<>', ' ');
            if not ValidTypes.Contains(OpportunityType) then
                Error('Invalid opportunity type: %1. Valid types are: Hardware with support, Hardware without support, Hardware + services, Service, Managed Service, Software, Tender Offer', OpportunityType);
        end;
    end;

    local procedure ValidateSerialNumber()
    var
        Opportunity: Record Opportunity;
    begin
        if ("Serial_Number__c" = '') or ("Support_Start_Date__c" = 0D) or ("Support_End_Date__c" = 0D) then
            exit;

        Opportunity.SetRange("Serial_Number__c", "Serial_Number__c");
        Opportunity.SetRange("Support_Start_Date__c", "Support_Start_Date__c");
        Opportunity.SetRange("Support_End_Date__c", "Support_End_Date__c");
        Opportunity.SetFilter("No.", '<>%1', "No.");

        if Opportunity.FindFirst() then
            Error('Serial Number %1 with Support Start Date %2 and Support End Date %3 already exists in Opportunity %4.',
                  "Serial_Number__c",
                  Format("Support_Start_Date__c"),
                  Format("Support_End_Date__c"),
                  Opportunity."No.");
    end;

    procedure CalculateMargins()
    begin
        if "Amount" <> 0 then
            "Margin_Percent__c" := ("Net_Margin__c" / "Amount") * 100
        else
            "Margin_Percent__c" := 0;
    end;

    procedure CalculateNetMargin()
    begin
        // Service or Managed Service: 13.18% of total amount
        if ("Type_of_Project__c" = "Type_of_Project__c"::Service) or
           ("Type_of_Project__c" = "Type_of_Project__c"::"Managed Service") or
           ("Type_of_Project_Multi__c".Contains('Service')) or
           ("Type_of_Project_Multi__c".Contains('Managed Service')) then
            "Net_Margin__c" := "Amount" * 0.1318
        else
            // License or Renewal with Blesk partner: 8.53% of total amount
            if ("Type_Of_Support__c" = "Type_Of_Support__c"::"New license") or
               ("Type_Of_Support__c" = "Type_Of_Support__c"::Renewal) or
               ("Type_Of_Support_Multi__c".Contains('New license')) or
               ("Type_Of_Support_Multi__c".Contains('Renewal')) then
                if "Partner_For_The_Project__c" = 'Blesk' then
                    "Net_Margin__c" := "Amount" * 0.0853
                else
                    // Hardware opportunities: Amount - Cost - (3.88% * amount)
                    if ("Type_of_Project__c" = "Type_of_Project__c"::"Hardware with support") or
                       ("Type_of_Project__c" = "Type_of_Project__c"::"Hardware without support") or
                       ("Type_of_Project__c" = "Type_of_Project__c"::"Hardware + services") or
                       ("Type_of_Project_Multi__c".Contains('Hardware')) then
                        "Net_Margin__c" := "Amount" - "Cost__c" - ("Amount" * 0.0388)
                    else
                        // Default: margin amount
                        "Net_Margin__c" := "Amount" - "Cost__c"
            else
                // Hardware opportunities: Amount - Cost - (3.88% * amount)
                if ("Type_of_Project__c" = "Type_of_Project__c"::"Hardware with support") or
                   ("Type_of_Project__c" = "Type_of_Project__c"::"Hardware without support") or
                   ("Type_of_Project__c" = "Type_of_Project__c"::"Hardware + services") or
                   ("Type_of_Project_Multi__c".Contains('Hardware')) then
                    "Net_Margin__c" := "Amount" - "Cost__c" - ("Amount" * 0.0388)
                else
                    // Default: margin amount
                    "Net_Margin__c" := "Amount" - "Cost__c";
    end;
}

pageextension 50100 "MyOpportunityCardExt" extends "Opportunity Card"
{
    layout
    {
        addafter("General")
        {
            group("SFHistoricalData")
            {
                Caption = 'SF - Historical Data';
                field("Amount"; Rec."Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the selling price to customer.';
                }
                field("Cost__c"; Rec."Cost__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the cost.';
                }

                field("Margin_Percent__c"; Rec."Margin_Percent__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the margin on the sale percentage.';
                    Editable = false;
                }
                field("Net_Margin__c"; Rec."Net_Margin__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the net margin. Calculation rules: Service/Managed Service = 13.18% of amount; License/Renewal with Blesk partner = 8.53% of amount; Hardware = Amount - Cost - (3.88% of amount); Other types = margin amount.';
                    Editable = false;
                }
                field("Type_of_Project_Multi__c"; Rec."Type_of_Project_Multi__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type(s) of opportunity. Click the lookup button to select multiple options.';
                    trigger OnAssistEdit()
                    begin
                        SelectOpportunityTypes();
                    end;
                }
                field("Type_of_Technology__c"; Rec."Type_of_Technology__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type(s) of technology. Click the lookup button to select multiple options.';
                    trigger OnAssistEdit()
                    begin
                        SelectTechnologyTypes();
                    end;
                }
                field("Type_Of_Support_Multi__c"; Rec."Type_Of_Support_Multi__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type(s) of support. Click the lookup button to select multiple options.';
                    trigger OnAssistEdit()
                    begin
                        SelectSupportTypes();
                    end;
                }
                field("Type_Of_Service__c"; Rec."Type_Of_Service__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of service.';
                }
                field("Type_Of_License__c"; Rec."Type_Of_License__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of license.';
                }
                field("PnL_Total_Hours__c"; Rec."PnL_Total_Hours__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total PnL hours.';
                }
                field("Partner_For_The_Project__c"; Rec."Partner_For_The_Project__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner for the project.';
                }
                field("Manufacturer__c"; Rec."Manufacturer__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the distributor.';
                }
                field("Serial_Number__c"; Rec."Serial_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the serial number.';
                    MultiLine = true;
                }
                field("Support_Start_Date__c"; Rec."Support_Start_Date__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the support start date.';
                }
                field("Support_End_Date__c"; Rec."Support_End_Date__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the support end date.';
                }
                field("CloseDate"; Rec."CloseDate")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the close date.';
                }
                field("Renewal_Date__c"; Rec."Renewal_Date__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the renewal date.';
                }
                field("Fiscal_Week__c"; Rec."Fiscal_Week__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the fiscal week.';
                }
                field("Fiscal_Year_Start__c"; Rec."Fiscal_Year_Start__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the fiscal year start date.';
                }
                field("Fiscal_Year_End__c"; Rec."Fiscal_Year_End__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the fiscal year end date.';
                }
                field("Tender_Offer_ID__c"; Rec."Tender_Offer_ID__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the tender offer ID.';
                }
                field("Technician_Assigned__c"; Rec."Technician_Assigned__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the assigned technician.';
                }
                field("Internal_Project_Name__c"; Rec."Internal_Project_Name__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the internal project name.';
                }
                field("Follow_Up_Reminder__c"; Rec."Follow_Up_Reminder__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the follow up reminder date.';
                }
                field("ERP_Quote_Number__c"; Rec."ERP_Quote_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the quote number.';
                }
                field("Partner_Quote_Number__c"; Rec."Partner_Quote_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner quote number.';
                }
                field("Client_PO_Number__c"; Rec."Client_PO_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the client PO number.';
                }
                field("Internal_PO_Number__c"; Rec."Internal_PO_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the internal PO number.';
                }
                field("Invoice__c"; Rec."Invoice__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the invoice.';
                }
                field("Salesforce OpportunityId"; Rec."SalesforceOppId")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the ID of the corresponding opportunity in Salesforce.';
                }
                field("SalesforceOppDesc"; LargeText)
                {
                    Caption = 'SF Description';
                    ApplicationArea = All;
                    MultiLine = true;
                    trigger OnValidate()
                    begin
                        SetLargeText(LargeText);
                    end;
                }
            }
            group("OutdatedFields")
            {
                Caption = 'Outdated Fields';
                Visible = false;
                field("Type_of_Project__c"; Rec."Type_of_Project__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of opportunity.';
                }
                field("Type_Of_Support__c"; Rec."Type_Of_Support__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of support.';
                }
                field("Technician_Group__c"; Rec."Technician_Group__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the technician group.';
                }
            }
        }
        addafter("Sales Document No.")
        {
            field("Campaign"; Rec."Campaign")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the campaign.';
            }
        }
        modify("Campaign No.")
        {
            Visible = false;
        }
        addafter("Closed")
        {
            field("EOS"; Rec."EOS")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies if this opportunity is EOS.';
                Editable = IsEOSEditable;
            }
            field("Estimated Delivery Date"; Rec."Estimated Delivery Date")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the estimated delivery date for the opportunity. This field is set when closing the opportunity as won.';
                Editable = false;
            }
            field("Delivered"; Rec."Delivered")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies if the opportunity has been delivered.';
            }
        }
    }

    actions
    {
        addlast(Processing)
        {
            action("Toggle Hold Status")
            {
                ApplicationArea = All;
                Caption = 'Toggle Hold Status';
                ToolTip = 'Toggles the opportunity between In Progress and On Hold status. Only available when status is In Progress or On Hold.';
                Image = Stop;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Enabled = (Rec.Status = Rec.Status::"In Progress") or (Rec.Status = Rec.Status::"On Hold");

                trigger OnAction()
                begin
                    ToggleHoldStatus();
                end;
            }
        }
    }

    var
        LargeText: Text;
        TempSupportTypeBeforeChange: Option "None","Installation","License Renewal","New license","Other";
        IsEOSEditable: Boolean;

    trigger OnAfterGetRecord()
    begin
        LargeText := GetLargeText();
        Rec.CalculateNetMargin();
        IsEOSEditable := (UserId = 'APALACIO');
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        OpportunityCheck: Record Opportunity;
        ValidationErrors: List of [Text];
        ErrorMessage: Text;
        ErrorText: Text;
    begin
        // Only validate when user is trying to save (OK action)
        if CloseAction <> ACTION::OK then
            exit(true);

        // Skip validation if record has been deleted
        if not OpportunityCheck.Get(Rec."No.") then
            exit(true);

        // Validation 1: Contact No. must be filled.
        if Rec."Contact No." = '' then
            ValidationErrors.Add(StrSubstNo('%1 must be filled in.', Rec.FieldCaption("Contact No.")));

        // Validation 2: Type of Opportunity must have a value.
        if (Rec."Type_of_Project__c" = Rec."Type_of_Project__c"::"None") and (Rec."Type_of_Project_Multi__c" = '') then
            ValidationErrors.Add('Type of Opportunity must have a value.');

        // Validation 3: Type of Technology must have a value.
        if Rec."Type_of_Technology__c" = '' then
            ValidationErrors.Add(StrSubstNo('%1 must have a value.', Rec.FieldCaption("Type_of_Technology__c")));

        // Validation 4: Rule for Type of Support based on Type of Opportunity.
        if (Rec."Type_of_Project_Multi__c" <> '') then
            if (Rec."Type_of_Project_Multi__c".Contains('Hardware with support')) or
               (Rec."Type_of_Project_Multi__c".Contains('Software')) or
               (Rec."Type_of_Project_Multi__c".Contains('Managed Service')) then
                if (Rec."Type_Of_Support_Multi__c" = '') then
                    ValidationErrors.Add('Type of Support must have a value when Type of Opportunity includes "Hardware with support", "Managed Service" or "Software".');

        // Validation 5: Rules for Renewal support type.
        if (Rec."Type_Of_Support_Multi__c" <> '') and (Rec."Type_Of_Support_Multi__c".Contains('Renewal')) then begin
            if Rec."Support_Start_Date__c" = 0D then
                ValidationErrors.Add(StrSubstNo('%1 is mandatory when Type of Support includes "Renewal".', Rec.FieldCaption("Support_Start_Date__c")));
            if Rec."Support_End_Date__c" = 0D then
                ValidationErrors.Add(StrSubstNo('%1 is mandatory when Type of Support includes "Renewal".', Rec.FieldCaption("Support_End_Date__c")));
        end;

        if ValidationErrors.Count = 0 then
            exit(true);

        ErrorMessage := 'The following errors were found:' + '\' + '\';
        foreach ErrorText in ValidationErrors do
            ErrorMessage += '- ' + ErrorText + '\' + '\';

        ErrorMessage += '\' + 'Do you want to exit regardless?';

        if Confirm(ErrorMessage, true) then
            exit(true) // Close the page
        else
            exit(false); // Stay on the page
    end;



    procedure SetLargeText(NewLargeText: Text)
    var
        OutStream: OutStream;
    begin
        Clear(Rec."SalesforceOppDesc");
        Rec."SalesforceOppDesc".CreateOutStream(OutStream, TEXTENCODING::UTF8);
        OutStream.WriteText(LargeText);
        Rec.Modify();
    end;

    procedure GetLargeText() NewLargeText: Text
    var
        TypeHelper: Codeunit "Type Helper";
        InStream: InStream;
    begin
        Rec.CalcFields("SalesforceOppDesc");
        Rec."SalesforceOppDesc".CreateInStream(InStream, TEXTENCODING::UTF8);
        exit(TypeHelper.TryReadAsTextWithSepAndFieldErrMsg(InStream, TypeHelper.LFSeparator(), Rec.FieldName("SalesforceOppDesc")));
    end;

    local procedure SelectTechnologyTypes()
    var
        TechnologyType: Text;
        TechnologyTypes: List of [Text];
        TempTechnologyType: Record "Technology Type" temporary;
        TechnologyTypePage: Page "Technology Type Selection";
    begin
        if Rec."Type_of_Technology__c" <> '' then
            TechnologyTypes := Rec."Type_of_Technology__c".Split(',');

        // Initialize temporary record with options
        TempTechnologyType.Init();
        TempTechnologyType.Code := 'CYBER';
        TempTechnologyType.Description := 'Cyber Security';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'NETWORK';
        TempTechnologyType.Description := 'Network';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'MONITOR';
        TempTechnologyType.Description := 'Monitoring';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'RTLS';
        TempTechnologyType.Description := 'RTLS';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'SYSINFRA';
        TempTechnologyType.Description := 'System Infra';
        TempTechnologyType.Insert();

        TempTechnologyType.Init();
        TempTechnologyType.Code := 'OTHER';
        TempTechnologyType.Description := 'Other';
        TempTechnologyType.Insert();

        // Set selected values
        foreach TechnologyType in TechnologyTypes do begin
            TechnologyType := DelChr(TechnologyType, '<>', ' ');
            TempTechnologyType.Reset();
            TempTechnologyType.SetRange(Description, TechnologyType);
            if TempTechnologyType.FindFirst() then begin
                TempTechnologyType.Selected := true;
                TempTechnologyType.Modify();
            end;
        end;

        TempTechnologyType.Reset();
        TechnologyTypePage.SetTempRecord(TempTechnologyType);
        if TechnologyTypePage.RunModal() = Action::OK then begin
            Clear(TempTechnologyType);
            TechnologyTypePage.GetSelectedTypes(TempTechnologyType);

            Rec."Type_of_Technology__c" := '';
            if TempTechnologyType.FindSet() then
                repeat
                    if Rec."Type_of_Technology__c" = '' then
                        Rec."Type_of_Technology__c" := TempTechnologyType.Description
                    else
                        Rec."Type_of_Technology__c" += ',' + TempTechnologyType.Description;
                until TempTechnologyType.Next() = 0;

            Rec.Modify();
            CurrPage.Update();
        end;
    end;

    local procedure SelectSupportTypes()
    var
        SupportType: Text;
        SupportTypes: List of [Text];
        TempSupportType: Record "Support Type" temporary;
        SupportTypePage: Page "Support Type Selection";
    begin
        if Rec."Type_Of_Support_Multi__c" <> '' then
            SupportTypes := Rec."Type_Of_Support_Multi__c".Split(',');

        // Initialize temporary record with options
        TempSupportType.Init();
        TempSupportType.Code := 'INSTALL';
        TempSupportType.Description := 'Installation';
        TempSupportType.Insert();

        TempSupportType.Init();
        TempSupportType.Code := 'RENEWAL';
        TempSupportType.Description := 'Renewal';
        TempSupportType.Insert();

        TempSupportType.Init();
        TempSupportType.Code := 'NEWLIC';
        TempSupportType.Description := 'New license';
        TempSupportType.Insert();

        TempSupportType.Init();
        TempSupportType.Code := 'NEWMS';
        TempSupportType.Description := 'New Managed Service';
        TempSupportType.Insert();

        TempSupportType.Init();
        TempSupportType.Code := 'OTHER';
        TempSupportType.Description := 'Other';
        TempSupportType.Insert();

        // Set selected values
        foreach SupportType in SupportTypes do begin
            SupportType := DelChr(SupportType, '<>', ' ');
            TempSupportType.Reset();
            TempSupportType.SetRange(Description, SupportType);
            if TempSupportType.FindFirst() then begin
                TempSupportType.Selected := true;
                TempSupportType.Modify();
            end;
        end;

        TempSupportType.Reset();
        SupportTypePage.SetTempRecord(TempSupportType);
        if SupportTypePage.RunModal() = Action::OK then begin
            Clear(TempSupportType);
            SupportTypePage.GetSelectedTypes(TempSupportType);

            Rec."Type_Of_Support_Multi__c" := '';
            if TempSupportType.FindSet() then
                repeat
                    if Rec."Type_Of_Support_Multi__c" = '' then
                        Rec."Type_Of_Support_Multi__c" := TempSupportType.Description
                    else
                        Rec."Type_Of_Support_Multi__c" += ',' + TempSupportType.Description;
                until TempSupportType.Next() = 0;

            Rec.Modify();
            CurrPage.Update();
        end;
    end;

    local procedure SelectOpportunityTypes()
    var
        OpportunityType: Text;
        OpportunityTypes: List of [Text];
        TempOpportunityType: Record "Opportunity Type" temporary;
        OpportunityTypePage: Page "Opportunity Type Selection";
    begin
        if Rec."Type_of_Project_Multi__c" <> '' then
            OpportunityTypes := Rec."Type_of_Project_Multi__c".Split(',');

        // Initialize temporary record with options
        TempOpportunityType.Init();
        TempOpportunityType.Code := 'HWSUP';
        TempOpportunityType.Description := 'Hardware with support';
        TempOpportunityType.Insert();

        TempOpportunityType.Init();
        TempOpportunityType.Code := 'HWNOSUP';
        TempOpportunityType.Description := 'Hardware without support';
        TempOpportunityType.Insert();

        TempOpportunityType.Init();
        TempOpportunityType.Code := 'HWSVC';
        TempOpportunityType.Description := 'Hardware + services';
        TempOpportunityType.Insert();

        TempOpportunityType.Init();
        TempOpportunityType.Code := 'SERVICE';
        TempOpportunityType.Description := 'Service';
        TempOpportunityType.Insert();

        TempOpportunityType.Init();
        TempOpportunityType.Code := 'MANAGED';
        TempOpportunityType.Description := 'Managed Service';
        TempOpportunityType.Insert();

        TempOpportunityType.Init();
        TempOpportunityType.Code := 'SOFTWARE';
        TempOpportunityType.Description := 'Software';
        TempOpportunityType.Insert();

        TempOpportunityType.Init();
        TempOpportunityType.Code := 'TENDER';
        TempOpportunityType.Description := 'Tender Offer';
        TempOpportunityType.Insert();

        // Set selected values
        foreach OpportunityType in OpportunityTypes do begin
            OpportunityType := DelChr(OpportunityType, '<>', ' ');
            TempOpportunityType.Reset();
            TempOpportunityType.SetRange(Description, OpportunityType);
            if TempOpportunityType.FindFirst() then begin
                TempOpportunityType.Selected := true;
                TempOpportunityType.Modify();
            end;
        end;

        TempOpportunityType.Reset();
        OpportunityTypePage.SetTempRecord(TempOpportunityType);
        if OpportunityTypePage.RunModal() = Action::OK then begin
            Clear(TempOpportunityType);
            OpportunityTypePage.GetSelectedTypes(TempOpportunityType);

            Rec."Type_of_Project_Multi__c" := '';
            if TempOpportunityType.FindSet() then
                repeat
                    if Rec."Type_of_Project_Multi__c" = '' then
                        Rec."Type_of_Project_Multi__c" := TempOpportunityType.Description
                    else
                        Rec."Type_of_Project_Multi__c" += ',' + TempOpportunityType.Description;
                until TempOpportunityType.Next() = 0;

            Rec.CalculateNetMargin();
            Rec.Modify();
            CurrPage.Update();
        end;
    end;

    local procedure ToggleHoldStatus()
    begin
        case Rec.Status of
            Rec.Status::"In Progress":
                begin
                    Rec.Status := Rec.Status::"On Hold";
                    Message('Opportunity %1 has been put on hold.', Rec."No.");
                end;
            Rec.Status::"On Hold":
                begin
                    Rec.Status := Rec.Status::"In Progress";
                    Message('Opportunity %1 has been resumed and is now in progress.', Rec."No.");
                end;
            else
                Error('This action is only available when the opportunity status is "In Progress" or "On Hold".');
        end;

        Rec.Modify();
        CurrPage.Update();
    end;

}