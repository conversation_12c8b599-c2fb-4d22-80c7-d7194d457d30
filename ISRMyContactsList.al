page 50145 "ISR My Contacts List"
{
    ApplicationArea = All;
    Caption = 'My Contacts';
    PageType = List;
    SourceTable = Contact;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact number';

                    trigger OnDrillDown()
                    begin
                        Page.Run(Page::"Contact Card", Rec);
                    end;
                }
                field(Name; Rec.Name)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name';
                }
                field("Company Name"; Rec."Company Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the company name';
                }
                field("Job Title"; Rec."Job Title")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the job title';
                }
                field("Phone No."; Rec."Phone No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phone number';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the email address';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the salesperson code';
                }
                field(Type; Rec.Type)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact type';
                }
                field(City; Rec.City)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the city';
                }
                field("Post Code"; Rec."Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the postal code';
                }
                field("Country/Region Code"; Rec."Country/Region Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the country/region code';
                }
                field("Date of Last Interaction"; Rec."Date of Last Interaction")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the date of the last interaction';
                }
                field(SalesforceAccountId; Rec.SalesforceAccountId)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the Salesforce Account ID';
                }
                field(CustomerNo; Rec.CustomerNo)
                {
                    ApplicationArea = All;
                    Caption = 'Customer No.';
                    ToolTip = 'Specifies the Customer No. from the linked Customer Card';
                    Editable = false;
                }
                field(Notes; Rec.Notes)
                {
                    ApplicationArea = All;
                    Caption = 'Notes';
                    ToolTip = 'Specifies any additional notes for the contact';
                }
            }
        }
        area(FactBoxes)
        {
            part("Contact Statistics"; "Contact Statistics FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "No." = field("No.");
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Refresh")
            {
                ApplicationArea = All;
                Caption = 'Refresh';
                Image = Refresh;
                ToolTip = 'Refresh the contact list';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    CurrPage.Update(false);
                end;
            }
        }
        area(Navigation)
        {
            action("Contact Card")
            {
                ApplicationArea = All;
                Caption = 'Contact Card';
                Image = ContactPerson;
                RunObject = page "Contact Card";
                RunPageLink = "No." = field("No.");
                ToolTip = 'Open the contact card';
                Promoted = true;
                PromotedCategory = Process;
            }
            action("Opportunities")
            {
                ApplicationArea = All;
                Caption = 'Opportunities';
                Image = Opportunity;
                RunObject = page "Opportunity List";
                RunPageLink = "Contact No." = field("No.");
                ToolTip = 'View opportunities for this contact';
                Promoted = true;
                PromotedCategory = Process;
            }
        }
        area(Creation)
        {
            action("New Contact")
            {
                ApplicationArea = All;
                Caption = 'New Contact';
                Image = ContactPerson;
                RunObject = page "Contact Card";
                RunPageMode = Create;
                ToolTip = 'Create a new contact';
                Promoted = true;
                PromotedCategory = New;
            }
        }
    }

    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
        SalespersonCode: Code[20];
    begin
        // Filter contacts by current user's salesperson code
        if UserSetup.Get(UserId) then
            SalespersonCode := UserSetup."Salespers./Purch. Code";

        if SalespersonCode <> '' then begin
            Rec.FilterGroup(2);
            Rec.SetRange("Salesperson Code", SalespersonCode);
            Rec.FilterGroup(0);
        end;

        // Sort by name
        Rec.SetCurrentKey(Name);
        Rec.Ascending(true);
    end;
}