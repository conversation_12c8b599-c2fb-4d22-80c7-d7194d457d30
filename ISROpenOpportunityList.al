page 50144 "ISR Open Opportunity List"
{
    ApplicationArea = All;
    Caption = 'My Open Opportunities';
    PageType = List;
    SourceTable = Opportunity;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity number';

                    trigger OnDrillDown()
                    begin
                        Page.Run(Page::"Opportunity Card", Rec);
                    end;
                }
                field(Closed; Rec.Closed)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies whether the opportunity is closed';
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity status';
                }
                field("Creation Date"; Rec."Creation Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies when the opportunity was created';
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity description';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the salesperson code';
                }
                field("Current Sales Cycle Stage"; Rec."Current Sales Cycle Stage")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the current sales cycle stage';
                }
                field("Contact No."; Rec."Contact No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact number';
                }
                field("Estimated Closing Date"; Rec."Estimated Closing Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the estimated closing date';
                }
                field("Date Closed"; Rec."Date Closed")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the actual closing date';
                }
                field("Estimated Value (LCY)"; Rec."Estimated Value (LCY)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the estimated value in local currency';
                }
                field("Calcd. Current Value (LCY)"; Rec."Calcd. Current Value (LCY)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the calculated current value in local currency';
                }
                field("Partner_For_The_Project__c"; Rec."Partner_For_The_Project__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner for the project';
                }
                field("Amount"; Rec."Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity amount';
                }
                field("Type_of_Project_Multi__c"; Rec."Type_of_Project_Multi__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of opportunity';
                }
                field("Type_of_Technology__c"; Rec."Type_of_Technology__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of technology';
                }
                field("Type_Of_Support_Multi__c"; Rec."Type_Of_Support_Multi__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of support';
                }
                field("Internal_PO_Number__c"; Rec."Internal_PO_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the internal PO number';
                }
                field("Client_PO_Number__c"; Rec."Client_PO_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the client PO number';
                }
                field("Partner_Quote_Number__c"; Rec."Partner_Quote_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner quote number';
                }
                field("ERP_Quote_Number__c"; Rec."ERP_Quote_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the ERP quote number';
                }
                field("Invoice__c"; Rec."Invoice__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the invoice';
                }
                field("Serial_Number__c"; Rec."Serial_Number__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the serial number';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Refresh")
            {
                ApplicationArea = All;
                Caption = 'Refresh';
                Image = Refresh;
                ToolTip = 'Refresh the opportunity list';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    CurrPage.Update(false);
                end;
            }
        }
        area(Navigation)
        {
            action("View Details")
            {
                ApplicationArea = All;
                Caption = 'View Details';
                Image = View;
                RunObject = page "Opportunity Card";
                RunPageLink = "No." = field("No.");
                ToolTip = 'Open the opportunity card to view details';
                Promoted = true;
                PromotedCategory = Process;
            }
        }
        area(Reporting)
        {
            action("Opportunity Report")
            {
                ApplicationArea = All;
                Caption = 'Opportunity Report';
                Image = Report;
                ToolTip = 'View opportunity reports';
            }
        }
    }

    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
        SalespersonCode: Code[20];
    begin
        // Filter opportunities by current user's salesperson code
        if UserSetup.Get(UserId) then
            SalespersonCode := UserSetup."Salespers./Purch. Code";

        if SalespersonCode <> '' then begin
            Rec.FilterGroup(2);
            Rec.SetRange("Salesperson Code", SalespersonCode);
            // Filter for open opportunities only (Not Started and In Progress)
            Rec.SetFilter(Status, '%1|%2', Rec.Status::"Not Started", Rec.Status::"In Progress");
            Rec.FilterGroup(0);
        end;

        // Sort by newest first (descending order) 
        Rec.SetCurrentKey("Creation Date");
        Rec.Ascending(false);
    end;
}