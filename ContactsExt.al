tableextension 50103 ContactsExtension extends "Contact"
{
    fields
    {
        field(50101; CustomerNo; Code[20])
        {
            Caption = 'Customer No.';
            FieldClass = FlowField;
            CalcFormula = Lookup("Contact Business Relation"."No." WHERE("Contact No." = FIELD("No."), "Business Relation Code" = CONST('CUST')));
        }
        field(50102; Notes; Text[2048])
        {
            Caption = 'Notes';
        }
        field(50100; SalesforceAccountId; Text[50])
        {
            Caption = 'Salesforce Account Id';
        }
        field(50103; "Total Interaction Score"; Decimal)
        {
            Caption = 'Total Interaction Score';
            FieldClass = FlowField;
            CalcFormula = Sum("Interaction Log Entry"."Interaction Score" WHERE("Contact No." = FIELD("No.")));
            Editable = false;
        }
        field(50104; Revenue; Decimal)
        {
            Caption = 'Revenue';
            DataClassification = CustomerContent;
        }
        field(50105; NumberOfEmployees; Integer)
        {
            Caption = 'Number of Employees';
            DataClassification = CustomerContent;
        }
        field(50106; "Exclude from Mass Mailing"; Boolean)
        {
            Caption = 'Exclude from Mass Mailing';
            ToolTip = 'Specifies whether this contact should be excluded from mass email campaigns.';
        }
        field(50107; "ABM TAL"; Boolean)
        {
            Caption = 'ABM TAL';
            ToolTip = 'Specifies whether this contact is part of the ABM TAL strategy.';
        }
    }
}

pageextension 50103 ContactsExtension extends "Contact List"
{
    layout
    {
        addafter("No.")
        {
            field("Date of Last Interaction"; Rec."Date of Last Interaction")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the date of the last interaction.';
            }
            field(Notes; Rec.Notes)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies any additional notes for the contact.';
            }
            field(Type; Rec.Type)
            {
                ApplicationArea = All;
            }
            field(Revenue; Rec.Revenue)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the revenue for the company contact.';
            }
            field(NumberOfEmployees; Rec.NumberOfEmployees)
            {
                ApplicationArea = All;
                Caption = 'Number of Employees';
                ToolTip = 'Specifies the number of employees for the company contact.';
            }
        }
        addafter("Territory Code")
        {
            field(SalesforceAccountId; Rec.SalesforceAccountId)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Salesforce Account ID.';
                Editable = false;
            }
            field("Total Interaction Score"; Rec."Total Interaction Score")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the sum of all interaction scores for this contact.';
                StyleExpr = TotalScoreStyleExpr;
            }
            field("Exclude from Mass Mailing"; Rec."Exclude from Mass Mailing")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies whether this contact should be excluded from mass email campaigns.';
            }
            field("ABM TAL"; Rec."ABM TAL")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies whether this contact is part of the ABM TAL strategy.';
            }
        }
        modify("Job Title")
        {
            Visible = true;
        }
        modify("E-Mail")
        {
            Editable = not IsCompany;
            ToolTip = 'Specifies the email address. Not editable for company contacts.';
        }
    }

    var
        TotalScoreStyleExpr: Text;
        IsCompany: Boolean;

    local procedure SetTotalScoreStyle()
    begin
        if Rec."Total Interaction Score" > 10 then
            TotalScoreStyleExpr := 'Favorable'
        else if Rec."Total Interaction Score" > 5 then
            TotalScoreStyleExpr := 'Ambiguous'
        else
            TotalScoreStyleExpr := 'Unfavorable';
    end;

    local procedure ValidateEmailUniqueness()
    var
        Contact: Record Contact;
        EmailAddress: Text[80];
    begin
        if Rec."E-Mail" = '' then
            exit;

        EmailAddress := Rec."E-Mail";
        Contact.Reset();
        Contact.SetRange("E-Mail", EmailAddress);
        Contact.SetFilter("No.", '<>%1', Rec."No.");

        if Contact.FindFirst() then
            Error('Email address %1 is already used by contact %2 (%3). Please use a different email address.',
                  EmailAddress, Contact."No.", Contact.Name);
    end;
}

pageextension 50104 ContactCardExtension extends "Contact Card"
{
    layout
    {
        addafter("Salesperson Code")
        {
            field("Total Interaction Score"; Rec."Total Interaction Score")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the sum of all interaction scores for this contact.';
                StyleExpr = TotalScoreStyleExpr;
            }
            field(SalesforceAccountId; Rec.SalesforceAccountId)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Salesforce Account ID.';
                Editable = false;
            }
            field(CustomerNo; Rec.CustomerNo)
            {
                ApplicationArea = All;
                Caption = 'Customer No.';
                ToolTip = 'Specifies the Customer No. from the linked Customer Card.';
                Editable = false;
            }
            field(Notes; Rec.Notes)
            {
                ApplicationArea = All;
                Caption = 'Notes';
                ToolTip = 'Specifies any additional notes for the contact.';
                MultiLine = true;
            }
            field("Exclude from Mass Mailing"; Rec."Exclude from Mass Mailing")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies whether this contact should be excluded from mass email campaigns.';
            }
            field("ABM TAL"; Rec."ABM TAL")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies whether this contact is part of the ABM TAL strategy.';
            }
            group(CompanyInfo)
            {
                Visible = IsCompany;
                ShowCaption = false;

                field(Revenue; Rec.Revenue)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the revenue for the company contact.';
                }
                field(NumberOfEmployees; Rec.NumberOfEmployees)
                {
                    ApplicationArea = All;
                    Caption = 'Number of Employees';
                    ToolTip = 'Specifies the number of employees for the company contact.';
                }
            }
        }
        addafter("General")
        {
            group("Time Bank")
            {
                Caption = 'Bank Hours';
                part(TimeBankLines; "Customer Time Bank Subform")
                {
                    ApplicationArea = All;
                    SubPageLink = "Account__c" = field(CustomerNo);
                }
            }
        }
        modify("Job Title")
        {
            Visible = true;
        }
        modify("E-Mail")
        {
            Editable = not IsCompany;
            ToolTip = 'Specifies the email address. Not editable for company contacts.';
        }
    }
    actions
    {
        modify("Oppo&rtunities")
        {
            Promoted = true;
            PromotedCategory = Process;
        }
    }

    trigger OnAfterGetRecord()
    begin
        SetTotalScoreStyle();
        IsCompany := Rec.Type = Rec.Type::Company;
    end;

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        IsCompany := Rec.Type = Rec.Type::Company;
    end;

    trigger OnAfterGetCurrRecord()
    begin
        IsCompany := Rec.Type = Rec.Type::Company;
        CurrPage.Update();
    end;

    trigger OnModifyRecord(): Boolean
    begin
        // Update IsCompany variable immediately when Type changes
        IsCompany := Rec.Type = Rec.Type::Company;

        // Prevent changing to Company if email exists
        if (Rec.Type = Rec.Type::Company) and (xRec.Type = Rec.Type::Person) and (Rec."E-Mail" <> '') then
            Error('Cannot change contact type to Company while email address exists. Please remove the email address first.');

        // Prevent adding email to company contacts
        if Rec.Type = Rec.Type::Company then
            if Rec."E-Mail" <> xRec."E-Mail" then
                Error('Email addresses cannot be added to company contacts.');

        // Validate email uniqueness for person contacts
        if (Rec.Type = Rec.Type::Person) and (Rec."E-Mail" <> xRec."E-Mail") and (Rec."E-Mail" <> '') then
            ValidateEmailUniqueness();

        exit(true);
    end;

    var
        TotalScoreStyleExpr: Text;
        IsCompany: Boolean;

    local procedure SetTotalScoreStyle()
    begin
        if Rec."Total Interaction Score" > 10 then
            TotalScoreStyleExpr := 'Favorable'
        else if Rec."Total Interaction Score" > 5 then
            TotalScoreStyleExpr := 'Ambiguous'
        else
            TotalScoreStyleExpr := 'Unfavorable';
    end;

    local procedure ValidateEmailUniqueness()
    var
        Contact: Record Contact;
        EmailAddress: Text[80];
    begin
        if Rec."E-Mail" = '' then
            exit;

        EmailAddress := Rec."E-Mail";
        Contact.Reset();
        Contact.SetRange("E-Mail", EmailAddress);
        Contact.SetFilter("No.", '<>%1', Rec."No.");

        if Contact.FindFirst() then
            Error('Email address %1 is already used by contact %2 (%3). Please use a different email address.',
                  EmailAddress, Contact."No.", Contact.Name);
    end;
}