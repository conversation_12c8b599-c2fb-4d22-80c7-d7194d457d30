// Enum for Mass Email Log Type
enum 50114 "Mass Email Log Type"
{
    Extensible = true;
    
    value(0; " ")
    {
        Caption = ' ';
    }
    value(1; "Job Started")
    {
        Caption = 'Job Started';
    }
    value(2; "Job Completed")
    {
        Caption = 'Job Completed';
    }
    value(3; "Job Failed")
    {
        Caption = 'Job Failed';
    }
    value(4; "Job Cancelled")
    {
        Caption = 'Job Cancelled';
    }
    value(5; "Email Sent")
    {
        Caption = 'Email Sent';
    }
    value(6; "Email Failed")
    {
        Caption = 'Email Failed';
    }
    value(7; "Batch Started")
    {
        Caption = 'Batch Started';
    }
    value(8; "Batch Completed")
    {
        Caption = 'Batch Completed';
    }
    value(9; "Throttle Wait")
    {
        Caption = 'Throttle Wait';
    }
    value(10; "Error")
    {
        Caption = 'Error';
    }
    value(11; "Warning")
    {
        Caption = 'Warning';
    }
    value(12; "Info")
    {
        Caption = 'Info';
    }
}
