table 50139 "Updated Opportunity"
{
    Caption = 'Updated Opportunity';
    TableType = Temporary;
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Line No."; Integer)
        {
            Caption = 'Line No.';
            DataClassification = CustomerContent;
        }
        field(2; "Opportunity No."; Code[20])
        {
            Caption = 'Opportunity No.';
            DataClassification = CustomerContent;
        }
        field(3; "Opportunity Name"; Text[100])
        {
            Caption = 'Opportunity Name';
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Line No.")
        {
            Clustered = true;
        }
        key(OpportunityNo; "Opportunity No.")
        {
            Unique = true;
        }
    }
}