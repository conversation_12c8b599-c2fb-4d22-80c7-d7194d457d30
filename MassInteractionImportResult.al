table 50146 "Mass Interaction Import Result"
{
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Entry No."; Integer)
        {
            AutoIncrement = true;
        }
        field(2; "Row No."; Integer)
        {
            Caption = 'Row No.';
        }
        field(3; "Contact Email"; Text[100])
        {
            Caption = 'Contact Email';
        }
        field(4; "Interaction Code"; Text[50])
        {
            Caption = 'Interaction Code';
        }
        field(5; "Campaign No."; Code[20])
        {
            Caption = 'Campaign No.';
        }
        field(6; Status; Option)
        {
            Caption = 'Status';
            OptionMembers = Imported,Skipped;
        }
        field(7; Message; Text[250])
        {
            Caption = 'Message';
        }
        field(8; "Contact No."; Code[20])
        {
            Caption = 'Contact No.';
        }
        field(9; "Contact Identifier"; Text[100])
        {
            Caption = 'Contact Identifier';
        }
        field(10; Comment; Text[250])
        {
            Caption = 'Comment';
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
    }
}