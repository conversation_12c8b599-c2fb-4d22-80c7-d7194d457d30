tableextension 50104 "Interaction Log Entry Ext" extends "Interaction Log Entry"
{
    fields
    {
        field(50100; "Interaction Score"; Decimal)
        {
            Caption = 'Interaction Score';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies a score value for this interaction based on the interaction template.';
            Editable = false;
        }
    }

    // Instead of relying on OnInsert trigger that isn't firing with the Create Interaction wizard
    // we will now use an event subscriber in the codeunit below
}

pageextension 50110 "Interaction Log Entries Ext" extends "Interaction Log Entries"
{
    layout
    {
        addlast(Control1)
        {
            field("Interaction Score"; Rec."Interaction Score")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies a score value for this interaction based on the interaction template.';
                Editable = false;
                Style = StandardAccent;
            }
        }
    }
}

codeunit 50120 "Interaction Score Management"
{
    // Fallback subscriber for direct inserts into the table
    [EventSubscriber(ObjectType::Table, Database::"Interaction Log Entry", 'OnAfterInsertEvent', '', false, false)]
    local procedure OnAfterInsertInteractionLogEntry(var Rec: Record "Interaction Log Entry"; RunTrigger: Boolean)
    begin
        SetInteractionScore(Rec);
    end;

    // Also monitor for modifications to catch updates
    [EventSubscriber(ObjectType::Table, Database::"Interaction Log Entry", 'OnAfterModifyEvent', '', false, false)]
    local procedure OnAfterModifyInteractionLogEntry(var Rec: Record "Interaction Log Entry"; var xRec: Record "Interaction Log Entry"; RunTrigger: Boolean)
    begin
        if Rec."Interaction Template Code" <> xRec."Interaction Template Code" then
            SetInteractionScore(Rec);
    end;

    local procedure SetInteractionScore(var InteractionLogEntry: Record "Interaction Log Entry")
    begin
        // Set the score based on template code
        case UpperCase(InteractionLogEntry."Interaction Template Code") of
            'WEBVIS(KP)':
                InteractionLogEntry."Interaction Score" := 5;
            'WEBVIS(OP)':
                InteractionLogEntry."Interaction Score" := 2.5;
            'CONTENTDL':
                InteractionLogEntry."Interaction Score" := 10;
            'FORMSUB':
                InteractionLogEntry."Interaction Score" := 15;
            'WEBATT(L)':
                InteractionLogEntry."Interaction Score" := 15;
            'EMAILCLK':
                InteractionLogEntry."Interaction Score" := 2.5;
            'EMAILREP':
                InteractionLogEntry."Interaction Score" := 20;
            'INTENTDATA':
                InteractionLogEntry."Interaction Score" := 20;
            'COLDMAILOP':
                InteractionLogEntry."Interaction Score" := 10;
            'COLDMAILRP':
                InteractionLogEntry."Interaction Score" := -20;
            'COLDCAL(P)':
                InteractionLogEntry."Interaction Score" := -20;
            'COLDCAL(N)':
                InteractionLogEntry."Interaction Score" := -20;
            'DMDEL':
                InteractionLogEntry."Interaction Score" := 10;
            'DMREP':
                InteractionLogEntry."Interaction Score" := 20;
            'LNKDMREP':
                InteractionLogEntry."Interaction Score" := 10;
            'LNKDMTGBKD':
                InteractionLogEntry."Interaction Score" := 25;
            'ACCADSFORM':
                InteractionLogEntry."Interaction Score" := 15;
            'CUSTEVENTA':
                InteractionLogEntry."Interaction Score" := 30;
            'CUSTEVENTF':
                InteractionLogEntry."Interaction Score" := 50;
            'SALESFLW':
                InteractionLogEntry."Interaction Score" := 25;
            'EMAILCLKO':
                InteractionLogEntry."Interaction Score" := 5;
            'EVENTINT':
                InteractionLogEntry."Interaction Score" := 2.5;
            'WEBREG':
                InteractionLogEntry."Interaction Score" := 10;
            'WEBREPLAY':
                InteractionLogEntry."Interaction Score" := 10;
            'EMAILMTGBK':
                InteractionLogEntry."Interaction Score" := 25;
            else
                InteractionLogEntry."Interaction Score" := 0; // Default score for other interaction types
        end;

        // Only use modify if the record exists in the database already
        if InteractionLogEntry.Modify() then;
    end;
}