// Table to store mass email jobs for background processing
table 50112 "Mass Email Queue"
{
    Caption = 'Mass Email Queue';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Job ID"; Code[20])
        {
            Caption = 'Job ID';
            DataClassification = CustomerContent;
        }
        field(2; "Job Status"; Enum "Mass Email Job Status")
        {
            Caption = 'Job Status';
            DataClassification = CustomerContent;
        }
        field(3; "Created Date Time"; DateTime)
        {
            Caption = 'Created Date Time';
            DataClassification = CustomerContent;
        }
        field(4; "Created By"; Code[50])
        {
            Caption = 'Created By';
            DataClassification = CustomerContent;
        }
        field(5; "Started Date Time"; DateTime)
        {
            Caption = 'Started Date Time';
            DataClassification = CustomerContent;
        }
        field(6; "Completed Date Time"; DateTime)
        {
            Caption = 'Completed Date Time';
            DataClassification = CustomerContent;
        }
        field(7; "Email Subject"; Text[250])
        {
            Caption = 'Email Subject';
            DataClassification = CustomerContent;
        }
        field(8; "Email Body"; Blob)
        {
            Caption = 'Email Body';
            DataClassification = CustomerContent;
        }
        field(9; "Salesperson Code"; Code[20])
        {
            Caption = 'Salesperson Code';
            DataClassification = CustomerContent;
            TableRelation = "Salesperson/Purchaser";
        }
        field(10; "Campaign No."; Code[20])
        {
            Caption = 'Campaign No.';
            DataClassification = CustomerContent;
            TableRelation = Campaign;
        }
        field(11; "Total Recipients"; Integer)
        {
            Caption = 'Total Recipients';
            DataClassification = CustomerContent;
        }
        field(12; "Emails Sent"; Integer)
        {
            Caption = 'Emails Sent';
            DataClassification = CustomerContent;
        }
        field(13; "Emails Failed"; Integer)
        {
            Caption = 'Emails Failed';
            DataClassification = CustomerContent;
        }
        field(14; "Last Error Message"; Text[250])
        {
            Caption = 'Last Error Message';
            DataClassification = CustomerContent;
        }
        field(15; "Job Queue Entry ID"; Guid)
        {
            Caption = 'Job Queue Entry ID';
            DataClassification = CustomerContent;
        }
        field(16; "Attachment Data"; Blob)
        {
            Caption = 'Attachment Data';
            DataClassification = CustomerContent;
        }
        field(17; "Attachment File Names"; Text[2048])
        {
            Caption = 'Attachment File Names';
            DataClassification = CustomerContent;
        }
        field(18; "Attachment Mime Types"; Text[2048])
        {
            Caption = 'Attachment Mime Types';
            DataClassification = CustomerContent;
        }
        field(19; "Progress Percentage"; Decimal)
        {
            Caption = 'Progress %';
            DataClassification = CustomerContent;
            DecimalPlaces = 0 : 2;
        }
        field(20; "Current Batch"; Integer)
        {
            Caption = 'Current Batch';
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Job ID")
        {
            Clustered = true;
        }
        key(Status; "Job Status", "Created Date Time")
        {
        }
    }

    trigger OnInsert()
    begin
        if "Job ID" = '' then
            "Job ID" := GetNextJobID();
        
        if "Created Date Time" = 0DT then
            "Created Date Time" := CurrentDateTime();
        
        if "Created By" = '' then
            "Created By" := CopyStr(UserId(), 1, MaxStrLen("Created By"));
        
        if "Job Status" = "Job Status"::" " then
            "Job Status" := "Job Status"::Pending;
    end;

    local procedure GetNextJobID(): Code[20]
    var
        MassEmailQueue: Record "Mass Email Queue";
        JobIDInt: Integer;
    begin
        MassEmailQueue.SetCurrentKey("Job ID");
        if MassEmailQueue.FindLast() then begin
            if Evaluate(JobIDInt, MassEmailQueue."Job ID") then
                JobIDInt += 1
            else
                JobIDInt := 1;
        end else
            JobIDInt := 1;
        
        exit(Format(JobIDInt));
    end;

    procedure SetEmailBody(EmailBodyText: Text)
    var
        OutStream: OutStream;
    begin
        "Email Body".CreateOutStream(OutStream, TEXTENCODING::UTF8);
        OutStream.WriteText(EmailBodyText);
    end;

    procedure GetEmailBody(): Text
    var
        InStream: InStream;
        EmailBodyText: Text;
    begin
        "Email Body".CreateInStream(InStream, TEXTENCODING::UTF8);
        InStream.ReadText(EmailBodyText);
        exit(EmailBodyText);
    end;

    procedure SetAttachmentData(var AttachmentTempBlobs: List of [Codeunit "Temp Blob"]; AttachmentFileNames: List of [Text]; AttachmentMimeTypes: List of [Text])
    var
        OutStream: OutStream;
        InStream: InStream;
        TempBlob: Codeunit "Temp Blob";
        i: Integer;
        FileNameText: Text;
        MimeTypeText: Text;
    begin
        // Store file names and mime types as delimited text
        FileNameText := '';
        MimeTypeText := '';
        
        for i := 1 to AttachmentFileNames.Count() do begin
            if i > 1 then begin
                FileNameText += '|';
                MimeTypeText += '|';
            end;
            FileNameText += AttachmentFileNames.Get(i);
            MimeTypeText += AttachmentMimeTypes.Get(i);
        end;
        
        "Attachment File Names" := CopyStr(FileNameText, 1, MaxStrLen("Attachment File Names"));
        "Attachment Mime Types" := CopyStr(MimeTypeText, 1, MaxStrLen("Attachment Mime Types"));
        
        // Store attachment data as concatenated blob
        if AttachmentTempBlobs.Count() > 0 then begin
            "Attachment Data".CreateOutStream(OutStream);
            for i := 1 to AttachmentTempBlobs.Count() do begin
                TempBlob := AttachmentTempBlobs.Get(i);
                TempBlob.CreateInStream(InStream);
                CopyStream(OutStream, InStream);
            end;
        end;
    end;

    procedure UpdateProgress()
    begin
        if "Total Recipients" > 0 then
            "Progress Percentage" := Round(("Emails Sent" + "Emails Failed") / "Total Recipients" * 100, 0.01)
        else
            "Progress Percentage" := 0;
    end;
}
