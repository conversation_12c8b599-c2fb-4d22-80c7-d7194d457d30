page 50112 "Salesforce Attachment Import"
{
    PageType = Card;
    Caption = 'Salesforce Attachment Import';
    UsageCategory = Tasks;
    ApplicationArea = All;
    SourceTable = "Temp Document Import";
    SourceTableTemporary = true;
    InsertAllowed = false;
    ModifyAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            group(Mapping)
            {
                Caption = 'Mapping';
                field(TotalMappingCount; TotalMappingCount)
                {
                    Caption = 'Total Mapping Records';
                    ApplicationArea = All;
                    Editable = false;
                }
            }
            group(Documents)
            {
                Caption = 'Documents';
                field(TotalDocumentCount; TotalDocumentCount)
                {
                    Caption = 'Total Documents';
                    ApplicationArea = All;
                    Editable = false;
                }
            }
            group(AttachmentsHelper)
            {
                Caption = 'Document Attachments';

                part(DocumentAttachmentListFactbox; "Doc. Attachment List Factbox")
                {
                    ApplicationArea = All;
                    Caption = 'Documents';
                    SubPageLink = "Table ID" = CONST(50137),
                                  "No." = CONST('0');
                    UpdatePropagation = SubPart;
                }
            }
            group(Results)
            {
                Caption = 'Results';
                Visible = ProcessingComplete;

                field(DocumentsCreated; DocumentsCreated)
                {
                    Caption = 'Documents Created';
                    ApplicationArea = All;
                    Editable = false;
                }
                field(DocumentsSkipped; DocumentsSkipped)
                {
                    Caption = 'Documents Skipped (Already Exist)';
                    ApplicationArea = All;
                    Editable = false;
                }
                field(OpportunitiesNotFound; OpportunitiesNotFound)
                {
                    Caption = 'Opportunities Not Found';
                    ApplicationArea = All;
                    Editable = false;
                }
            }
            group(UpdatedOpportunitiesGrp)
            {
                Caption = 'Updated Opportunities';
                Visible = ProcessingComplete;

                part(UpdatedOpportunitiesPart; "Updated Opportunity List")
                {
                    ApplicationArea = All;
                    Caption = 'Updated Opportunities';
                    UpdatePropagation = Both;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ImportMapping)
            {
                Caption = '1. Import CSV Mapping';
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Import;

                trigger OnAction()
                begin
                    ClearAll();
                    SalesforceMgt.ImportCSVMapping();
                    TotalMappingCount := SalesforceMgt.GetTotalMappingsCount();
                    Message('Successfully imported %1 mapping records.', TotalMappingCount);
                end;
            }
            action(GetDocuments)
            {
                Caption = '2. Scan for Documents';
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Document;

                trigger OnAction()
                begin
                    if TotalMappingCount = 0 then
                        Error('Please import the CSV mapping first.');

                    SalesforceMgt.ImportDocuments();
                    TotalDocumentCount := SalesforceMgt.GetTotalDocumentsCount();

                    if TotalDocumentCount > 0 then
                        Message('Found %1 documents ready for processing.', TotalDocumentCount);
                end;
            }
            action(ProcessAttachments)
            {
                Caption = '3. Process Attachments';
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = ApplyEntries;

                trigger OnAction()
                var
                    ConfirmQst: Label 'This will attach %1 documents to opportunities based on the CSV mapping. Continue?';
                begin
                    if TotalMappingCount = 0 then
                        Error('Please import the CSV mapping first.');

                    if TotalDocumentCount = 0 then
                        Error('Please scan for documents first.');

                    if not Confirm(ConfirmQst, false, TotalDocumentCount) then
                        exit;

                    SalesforceMgt.ProcessAttachments();

                    DocumentsCreated := SalesforceMgt.GetAttachmentsCreatedCount();
                    DocumentsSkipped := SalesforceMgt.GetAttachmentsSkippedCount();
                    OpportunitiesNotFound := SalesforceMgt.GetOpportunitiesNotFoundCount();

                    // Retrieve updated opportunities list
                    SalesforceMgt.GetUpdatedOpportunities(UpdatedOpportunities);

                    // Pass data to the subpage and refresh it to display results
                    CurrPage.UpdatedOpportunitiesPart.PAGE.SetSource(UpdatedOpportunities);
                    CurrPage.UpdatedOpportunitiesPart.PAGE.Update(false);

                    ProcessingComplete := true;

                    Message('Processing complete.\Document attachments created: %1\Already existing (skipped): %2\Opportunities not found: %3',
                        DocumentsCreated, DocumentsSkipped, OpportunitiesNotFound);
                end;
            }
            action(ClearData)
            {
                Caption = 'Clear All Data';
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = ClearLog;

                trigger OnAction()
                begin
                    if Confirm('Are you sure you want to clear all data?', false) then
                        ClearAll();
                end;
            }
            action(AddDummyRecord)
            {
                Caption = 'Initialize Attachment Area';
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Refresh;
                Visible = false; // Hide this action as it's handled automatically

                trigger OnAction()
                begin
                    CreateDummyRecord();
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        ClearAll();
        CreateDummyRecord();
    end;

    var
        SalesforceMgt: Codeunit "Salesforce Attachment Mgt.";
        TotalMappingCount: Integer;
        TotalDocumentCount: Integer;
        DocumentsCreated: Integer;
        DocumentsSkipped: Integer;
        OpportunitiesNotFound: Integer;
        ProcessingComplete: Boolean;
        UpdatedOpportunities: Record "Updated Opportunity" temporary;

    local procedure ClearAll()
    begin
        SalesforceMgt.ClearImportedData();

        TotalMappingCount := 0;
        TotalDocumentCount := 0;
        DocumentsCreated := 0;
        DocumentsSkipped := 0;
        OpportunitiesNotFound := 0;
        ProcessingComplete := false;

        UpdatedOpportunities.Reset();
        UpdatedOpportunities.DeleteAll();

        // Ensure the updated opportunities subpage is cleared as well
        CurrPage.UpdatedOpportunitiesPart.PAGE.SetSource(UpdatedOpportunities);

        CurrPage.Update(false);
    end;

    local procedure CreateDummyRecord()
    var
        DocAttachment: Record "Document Attachment";
    begin
        Rec.Reset();
        Rec.DeleteAll();

        Rec.Init();
        Rec."Entry No." := 1;
        Rec."Document Name" := 'Attach your documents here';
        Rec.Insert();

        // Ensure we have at least one document attachment record for the factbox
        DocAttachment.Reset();
        DocAttachment.SetRange("Table ID", 50137);
        DocAttachment.SetRange("No.", '0');
        if not DocAttachment.FindFirst() then begin
            DocAttachment.Init();
            DocAttachment."Table ID" := 50137;
            DocAttachment."No." := '0';
            DocAttachment.Insert();
        end;

        CurrPage.Update(false);
    end;
}