// Page to view detailed mass email logs
page 50115 "Mass Email Logs"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    Caption = 'Mass Email Logs';
    SourceTable = "Mass Email Log";
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(Logs)
            {
                field("Entry No."; Rec."Entry No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Unique entry number for this log record.';
                }
                field("Job ID"; Rec."Job ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Mass email job identifier.';
                }
                field("Log Date Time"; Rec."Log Date Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Date and time when this log entry was created.';
                }
                field("Log Type"; Rec."Log Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Type of log entry (e.g., Email Sent, Email Failed, Job Started).';
                    StyleExpr = LogTypeStyleExpr;
                }
                field("Contact No."; Rec."Contact No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Contact number for email-related log entries.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Contact name for email-related log entries.';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ToolTip = 'Email address for email-related log entries.';
                }
                field("Email Subject"; Rec."Email Subject")
                {
                    ApplicationArea = All;
                    ToolTip = 'Subject of the email for email-related log entries.';
                }
                field("Send Status"; Rec."Send Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Send status for email-related log entries.';
                    StyleExpr = SendStatusStyleExpr;
                }
                field("Processing Time (ms)"; Rec."Processing Time (ms)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Time taken to process this email in milliseconds.';
                }
                field("Batch No."; Rec."Batch No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Batch number for email processing.';
                }
                field("Retry Attempt"; Rec."Retry Attempt")
                {
                    ApplicationArea = All;
                    ToolTip = 'Number of retry attempts for failed emails.';
                }
                field("Campaign No."; Rec."Campaign No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Campaign associated with this email.';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Salesperson associated with this email.';
                }
                field("User ID"; Rec."User ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'User who initiated the mass email job.';
                }
                field("Error Message"; Rec."Error Message")
                {
                    ApplicationArea = All;
                    ToolTip = 'Error message for failed operations.';
                    Style = Unfavorable;
                }
                field("Additional Info"; Rec."Additional Info")
                {
                    ApplicationArea = All;
                    ToolTip = 'Additional information about this log entry.';
                }
            }
        }

        area(FactBoxes)
        {
            part(LogDetails; "Mass Email Log Details")
            {
                ApplicationArea = All;
                SubPageLink = "Entry No." = field("Entry No.");
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(FilterByJob)
            {
                ApplicationArea = All;
                Caption = 'Filter by Job';
                Image = Filter;
                ToolTip = 'Filter logs to show only entries for a specific job.';

                trigger OnAction()
                begin
                    FilterByJobID();
                end;
            }

            action(FilterByContact)
            {
                ApplicationArea = All;
                Caption = 'Filter by Contact';
                Image = ContactFilter;
                ToolTip = 'Filter logs to show only entries for a specific contact.';

                trigger OnAction()
                begin
                    FilterByContact();
                end;
            }

            action(FilterByStatus)
            {
                ApplicationArea = All;
                Caption = 'Filter by Status';
                Image = FilterLines;
                ToolTip = 'Filter logs by send status (Sent, Failed, etc.).';

                trigger OnAction()
                begin
                    FilterByStatus();
                end;
            }

            action(FilterByLogType)
            {
                ApplicationArea = All;
                Caption = 'Filter by Log Type';
                Image = FilterLines;
                ToolTip = 'Filter logs by log type (Email Sent, Job Started, etc.).';

                trigger OnAction()
                begin
                    FilterByLogType();
                end;
            }

            action(ClearFilters)
            {
                ApplicationArea = All;
                Caption = 'Clear Filters';
                Image = ClearFilter;
                ToolTip = 'Remove all filters and show all log entries.';

                trigger OnAction()
                begin
                    Rec.Reset();
                    CurrPage.Update(false);
                end;
            }

            action(ExportLogs)
            {
                ApplicationArea = All;
                Caption = 'Export to Excel';
                Image = ExportToExcel;
                ToolTip = 'Export the current log view to Excel.';

                trigger OnAction()
                begin
                    ExportToExcel();
                end;
            }

            action(ViewJobDetails)
            {
                ApplicationArea = All;
                Caption = 'View Job Details';
                Image = JobJournal;
                ToolTip = 'View details of the mass email job for this log entry.';
                Enabled = HasJobID;

                trigger OnAction()
                begin
                    ViewJobDetails();
                end;
            }
        }

        area(Promoted)
        {
            group(Filters)
            {
                Caption = 'Filters';
                actionref(FilterByJobRef; FilterByJob)
                {
                }
                actionref(FilterByContactRef; FilterByContact)
                {
                }
                actionref(FilterByStatusRef; FilterByStatus)
                {
                }
                actionref(ClearFiltersRef; ClearFilters)
                {
                }
            }

            group(Actions)
            {
                Caption = 'Actions';
                actionref(ExportLogsRef; ExportLogs)
                {
                }
                actionref(ViewJobDetailsRef; ViewJobDetails)
                {
                }
            }
        }
    }

    var
        LogTypeStyleExpr: Text;
        SendStatusStyleExpr: Text;
        HasJobID: Boolean;

    trigger OnAfterGetRecord()
    begin
        SetStyleExpressions();
        SetActionVisibility();
    end;

    local procedure SetStyleExpressions()
    begin
        // Set style for Log Type
        case Rec."Log Type" of
            Rec."Log Type"::"Email Sent":
                LogTypeStyleExpr := 'Favorable';
            Rec."Log Type"::"Email Failed":
                LogTypeStyleExpr := 'Unfavorable';
            Rec."Log Type"::"Job Started":
                LogTypeStyleExpr := 'StandardAccent';
            Rec."Log Type"::"Job Completed":
                LogTypeStyleExpr := 'Favorable';
            Rec."Log Type"::"Job Failed":
                LogTypeStyleExpr := 'Unfavorable';
            Rec."Log Type"::"Job Cancelled":
                LogTypeStyleExpr := 'Subordinate';
            Rec."Log Type"::Error:
                LogTypeStyleExpr := 'Unfavorable';
            Rec."Log Type"::Warning:
                LogTypeStyleExpr := 'Attention';
            else
                LogTypeStyleExpr := 'Standard';
        end;

        // Set style for Send Status
        case Rec."Send Status" of
            Rec."Send Status"::Sent:
                SendStatusStyleExpr := 'Favorable';
            Rec."Send Status"::Failed:
                SendStatusStyleExpr := 'Unfavorable';
            Rec."Send Status"::Skipped:
                SendStatusStyleExpr := 'Subordinate';
            else
                SendStatusStyleExpr := 'Standard';
        end;
    end;

    local procedure SetActionVisibility()
    begin
        HasJobID := Rec."Job ID" <> '';
    end;

    local procedure FilterByJobID()
    var
        JobID: Code[20];
    begin
        JobID := Rec."Job ID";
        if JobID = '' then begin
            Message('Please select a log entry with a Job ID first.');
            exit;
        end;

        Rec.SetRange("Job ID", JobID);
        CurrPage.Update(false);
        Message('Filtered to show logs for Job ID: %1', JobID);
    end;

    local procedure FilterByContact()
    var
        ContactNo: Code[20];
    begin
        ContactNo := Rec."Contact No.";
        if ContactNo = '' then begin
            Message('Please select a log entry with a Contact No. first.');
            exit;
        end;

        Rec.SetRange("Contact No.", ContactNo);
        CurrPage.Update(false);
        Message('Filtered to show logs for Contact: %1', ContactNo);
    end;

    local procedure FilterByStatus()
    var
        StatusOptions: Text;
        SelectedStatus: Integer;
    begin
        StatusOptions := 'Pending,Sent,Failed,Skipped';
        SelectedStatus := StrMenu(StatusOptions, 0, 'Select status to filter by:');

        if SelectedStatus > 0 then begin
            case SelectedStatus of
                1: Rec.SetRange("Send Status", Rec."Send Status"::Pending);
                2: Rec.SetRange("Send Status", Rec."Send Status"::Sent);
                3: Rec.SetRange("Send Status", Rec."Send Status"::Failed);
                4: Rec.SetRange("Send Status", Rec."Send Status"::Skipped);
            end;
            CurrPage.Update(false);
            Message('Filtered to show logs with selected status.');
        end;
    end;

    local procedure FilterByLogType()
    var
        LogTypeOptions: Text;
        SelectedLogType: Integer;
    begin
        LogTypeOptions := 'Job Started,Job Completed,Job Failed,Email Sent,Email Failed,Batch Started,Error,Warning,Info';
        SelectedLogType := StrMenu(LogTypeOptions, 0, 'Select log type to filter by:');

        if SelectedLogType > 0 then begin
            case SelectedLogType of
                1: Rec.SetRange("Log Type", Rec."Log Type"::"Job Started");
                2: Rec.SetRange("Log Type", Rec."Log Type"::"Job Completed");
                3: Rec.SetRange("Log Type", Rec."Log Type"::"Job Failed");
                4: Rec.SetRange("Log Type", Rec."Log Type"::"Email Sent");
                5: Rec.SetRange("Log Type", Rec."Log Type"::"Email Failed");
                6: Rec.SetRange("Log Type", Rec."Log Type"::"Batch Started");
                7: Rec.SetRange("Log Type", Rec."Log Type"::Error);
                8: Rec.SetRange("Log Type", Rec."Log Type"::Warning);
                9: Rec.SetRange("Log Type", Rec."Log Type"::Info);
            end;
            CurrPage.Update(false);
            Message('Filtered to show logs with selected type.');
        end;
    end;

    local procedure ExportToExcel()
    var
        TempExcelBuffer: Record "Excel Buffer" temporary;
        RowNo: Integer;
    begin
        TempExcelBuffer.DeleteAll();
        RowNo := 1;

        // Add headers
        TempExcelBuffer.NewRow();
        TempExcelBuffer.AddColumn('Entry No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Job ID', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Log Date Time', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Log Type', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Contact No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Contact Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('E-Mail', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Send Status', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Error Message', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);

        // Add data
        if Rec.FindSet() then
            repeat
                TempExcelBuffer.NewRow();
                TempExcelBuffer.AddColumn(Format(Rec."Entry No."), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
                TempExcelBuffer.AddColumn(Rec."Job ID", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Rec."Log Date Time"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Rec."Log Type"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."Contact No.", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."Contact Name", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."E-Mail", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Rec."Send Status"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."Error Message", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
            until Rec.Next() = 0;

        TempExcelBuffer.CreateNewBook('Mass Email Logs');
        TempExcelBuffer.WriteSheet('Logs', CompanyName(), UserId());
        TempExcelBuffer.CloseBook();
        TempExcelBuffer.SetFriendlyFilename('MassEmailLogs');
        TempExcelBuffer.OpenExcel();
    end;

    local procedure ViewJobDetails()
    var
        MassEmailQueue: Record "Mass Email Queue";
        MassEmailJobCard: Page "Mass Email Job Card";
    begin
        if Rec."Job ID" = '' then begin
            Message('No Job ID associated with this log entry.');
            exit;
        end;

        if MassEmailQueue.Get(Rec."Job ID") then begin
            MassEmailJobCard.SetRecord(MassEmailQueue);
            MassEmailJobCard.Run();
        end else
            Message('Job %1 not found.', Rec."Job ID");
    end;
}
