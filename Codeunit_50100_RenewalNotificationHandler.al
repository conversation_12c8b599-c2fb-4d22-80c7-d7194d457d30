codeunit 50100 "RenewalNotificationHandler"
{
    procedure CheckRenewalDatesAndNotify()
    var
        Opportunity: Record "Opportunity";
        UserSetup: Record "User Setup";
        Email: Codeunit "Email";
        EmailMessage: Codeunit "Email Message";
        ThreeMonthsFromNow: Date;
        TwoMonthsFromNow: Date;
        OneMonthFromNow: Date;
        SevenDaysFromNow: Date;
        TodayDate: Date;
        Recipients: List of [Text];
        Subject: Text;
        Body: Text;
        SalespersonEmail: Text;
    begin
        TodayDate := Today();
        ThreeMonthsFromNow := CalcDate('<3M>', TodayDate);
        TwoMonthsFromNow := CalcDate('<2M>', TodayDate);
        OneMonthFromNow := CalcDate('<1M>', TodayDate);
        SevenDaysFromNow := CalcDate('<7D>', TodayDate);

        Message('Checking renewals for dates: 3M=%1, 2M=%2, 1M=%3, 7D=%4',
                ThreeMonthsFromNow, TwoMonthsFromNow, OneM<PERSON>h<PERSON>romNow, SevenDaysFromNow);

        // Check for all notification dates
        Opportunity.Reset();
        Opportunity.SetFilter("Support_End_Date__c", '%1|%2|%3|%4',
                             ThreeMonthsFromNow, TwoMonthsFromNow, OneMonthFromNow, SevenDaysFromNow);
        // Only consider opportunities that are already won
        Opportunity.SetRange("Status", Opportunity.Status::Won);

        if Opportunity.IsEmpty() then
            Message('No opportunities found.');

        if Opportunity.FindSet() then
            repeat
                SalespersonEmail := GetRecipientEmail(Opportunity);
                Recipients.RemoveRange(1, Recipients.Count());
                if SalespersonEmail <> '' then
                    Recipients.Add(SalespersonEmail);
                Recipients.Add('<EMAIL>');
                Recipients.Add('<EMAIL>');

                if Recipients.Count() = 0 then
                    Error('No recipients for Opportunity %1.', Opportunity."No.");

                Subject := 'Renewal Reminder: Opportunity ' + Opportunity."No.";
                Body := GetEmailBody(Opportunity);

                EmailMessage.Create(Recipients, Subject, Body, true);
                Message('Attempting to send email to %1 for Opportunity %2', SalespersonEmail, Opportunity."No."); // Debug

                // Send email using default email account
                if not Email.Send(EmailMessage) then
                    Error('Failed to send renewal notification for Opportunity %1.', Opportunity."No.");
            until Opportunity.Next() = 0;
    end;

    local procedure GetEmailBody(Opportunity: Record "Opportunity"): Text
    var
        TodayDate: Date;
        DaysUntilRenewal: Integer;
        RenewalTimeframe: Text;
        Body: Text;
    begin
        TodayDate := Today();
        DaysUntilRenewal := Opportunity."Support_End_Date__c" - TodayDate;

        case true of
            DaysUntilRenewal = 90:
                RenewalTimeframe := '3 months';
            DaysUntilRenewal = 60:
                RenewalTimeframe := '2 months';
            DaysUntilRenewal = 30:
                RenewalTimeframe := '1 month';
            DaysUntilRenewal = 7:
                RenewalTimeframe := '7 days';
            else
                RenewalTimeframe := Format(DaysUntilRenewal) + ' days';
        end;

        Body := '<html><body style="font-family: Arial, sans-serif; color: #333333;">' +
                '<h3 style="color: #2c3e50;">Dear Team,</h3>' +
                '<p style="line-height: 1.6;">' +
                'The opportunity <strong>"' + Opportunity.Description + '"</strong> ' +
                '(ID: ' + Opportunity."No." + ') is due for renewal in <strong>' + RenewalTimeframe + '</strong> on ' +
                '<span style="color: #e74c3c;">' + Format(Opportunity."Support_End_Date__c") + '</span>. ' +
                'Please review and take necessary action.' +
                '</p>' +
                '<p style="line-height: 1.6;">' +
                '<strong>Salesperson:</strong> ' + Opportunity."Salesperson Code" +
                '</p>' +
                '<p style="line-height: 1.6;">' +
                'Regards,<br>' +
                '<em>Prival Business Central System</em>' +
                '</p>' +
                '</body></html>';

        exit(Body);
    end;

    local procedure GetRecipientEmail(Opportunity: Record "Opportunity"): Text
    var
        SalespersonPurchaser: Record "Salesperson/Purchaser";
    begin
        if Opportunity."Salesperson Code" <> '' then begin
            SalespersonPurchaser.SetRange(Code, Opportunity."Salesperson Code");
            if SalespersonPurchaser.FindFirst() then begin
                if SalespersonPurchaser."E-Mail" <> '' then
                    exit(SalespersonPurchaser."E-Mail")
                else
                    Message('Salesperson %1 has no email.', SalespersonPurchaser.Code);
            end else
                Message('No Salesperson found for code %1.', Opportunity."Salesperson Code");
        end;
        exit('');
    end;
}

report 50100 "TestRenewalNotificationReport"
{
    UsageCategory = Tasks;
    ApplicationArea = All;
    Caption = 'Renewal Notification';
    ProcessingOnly = true;

    trigger OnPostReport()
    var
        RenewalNotificationHandler: Codeunit "RenewalNotificationHandler";
    begin
        RenewalNotificationHandler.CheckRenewalDatesAndNotify();
        Message('Completed.');
    end;
}