// Codeunit for managing mass email operations
codeunit 50111 "Mass Email Management"
{
    procedure LoadContactsForSalesperson(SalespersonCode: Code[20]; var MassEmailContactBuffer: Record "Mass Email Contact Buffer")
    var
        Contact: Record Contact;
    begin
        MassEmailContactBuffer.Reset();
        MassEmailContactBuffer.DeleteAll();

        if SalespersonCode = '' then
            exit;

        Contact.Reset();
        Contact.SetRange("Salesperson Code", SalespersonCode);
        Contact.SetFilter("E-Mail", '<>%1', '');
        Contact.SetRange("Exclude from Mass Mailing", false);

        if Contact.FindSet() then
            repeat
                MassEmailContactBuffer.Init();
                MassEmailContactBuffer."Contact No." := Contact."No.";
                MassEmailContactBuffer."Contact Name" := Contact.Name;
                MassEmailContactBuffer."E-Mail" := Contact."E-Mail";
                MassEmailContactBuffer."Company Name" := Contact."Company Name";
                MassEmailContactBuffer."Salesperson Code" := Contact."Salesperson Code";
                MassEmailContactBuffer.Selected := true; // Default to selected
                MassEmailContactBuffer.Insert();
            until Contact.Next() = 0;
    end;

    procedure GetUserEmail(): Text[80]
    begin
        // Don't try to get email account explicitly - let BC email system handle it
        // Return a placeholder that indicates the system will use the configured account
        exit('System will use your configured email account');
    end;

    procedure GetCurrentUserSalespersonCode(): Code[20]
    var
        UserSetup: Record "User Setup";
    begin
        if UserSetup.Get(UserId()) then
            exit(UserSetup."Salespers./Purch. Code");

        exit('');
    end;

    procedure SendMassEmail(var MassEmailContactBuffer: Record "Mass Email Contact Buffer"; Subject: Text[250]; MessageBody: Text; var AttachmentInStream: InStream; AttachmentName: Text; AttachmentMimeType: Text): Boolean
    var
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        BCCRecipients: Text;
        FromEmail: Text[80];
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
        RecipientCount: Integer;
    begin
        FromEmail := GetUserEmail();
        if FromEmail = '' then begin
            Message('No email account found. Please configure your email account in Business Central email settings or contact your administrator.');
            exit(false);
        end;

        // Build recipients list from selected contacts
        BCCRecipients := '';
        TempMassEmailContactBuffer.Copy(MassEmailContactBuffer, true);
        TempMassEmailContactBuffer.Reset();
        TempMassEmailContactBuffer.SetRange(Selected, true);

        if not TempMassEmailContactBuffer.FindSet() then begin
            Message('No contacts selected for email.');
            exit(false);
        end;

        RecipientCount := 0;
        repeat
            if BCCRecipients <> '' then
                BCCRecipients += ';';
            BCCRecipients += TempMassEmailContactBuffer."E-Mail";
            RecipientCount += 1;
        until TempMassEmailContactBuffer.Next() = 0;

        // Create email message with recipients
        // Note: Recipients are added as TO, but each recipient only sees their own email address
        EmailMessage.Create(BCCRecipients, Subject, MessageBody, true);

        // Add attachment if provided
        if AttachmentName <> '' then
            EmailMessage.AddAttachment(AttachmentName, AttachmentMimeType, AttachmentInStream);

        // Send the email
        if Email.Send(EmailMessage) then begin
            exit(true);
        end else begin
            Message('Failed to send mass email. Please check:\n- Your email account configuration\n- Email server settings\n- Network connectivity\n\nCheck the Email Outbox for more details.');
            exit(false);
        end;
    end;

    procedure GetSelectedContactCount(var MassEmailContactBuffer: Record "Mass Email Contact Buffer"): Integer
    var
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
    begin
        TempMassEmailContactBuffer.Copy(MassEmailContactBuffer, true);
        TempMassEmailContactBuffer.Reset();
        TempMassEmailContactBuffer.SetRange(Selected, true);
        exit(TempMassEmailContactBuffer.Count());
    end;
}
