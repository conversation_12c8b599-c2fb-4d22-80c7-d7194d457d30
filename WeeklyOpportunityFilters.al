// Weekly Won Opportunities List
page 50101 "Weekly Won Opportunities"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = Opportunity;
    Caption = 'Weekly Won Opportunities';
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("No."; Rec."No.") { ApplicationArea = All; }
                field(Description; Rec.Description) { ApplicationArea = All; }
                field(Type_of_Project_Multi; Rec.Type_of_Project_Multi__c) { ApplicationArea = All; Caption = 'Type of Opportunity'; }
                field(Type_Of_Support_Multi; Rec.Type_Of_Support_Multi__c) { ApplicationArea = All; Caption = 'Type of Support'; }
                field("Salesperson Code"; Rec."Salesperson Code") { ApplicationArea = All; }
                field(Type_of_Technology; Rec.Type_of_Technology__c) { ApplicationArea = All; Caption = 'Type of Technology'; }
                field("Contact Name"; Rec."Contact Name") { ApplicationArea = All; Caption = 'Client Name'; }
                field(Partner_For_The_Project; Rec.Partner_For_The_Project__c) { ApplicationArea = All; Caption = 'Partner'; }
                field("Sales Cycle Code"; Rec."Sales Cycle Code") { ApplicationArea = All; }
                field("Creation Date"; Rec."Creation Date") { ApplicationArea = All; }
                field("Date Closed"; Rec."Date Closed") { ApplicationArea = All; }
                field(Amount; Rec.Amount) { ApplicationArea = All; }
                field(Margin_Percent; Rec."Margin_Percent__c") { ApplicationArea = All; Caption = 'Margin on the sale (%)'; }
                field(Net_Margin; Rec."Net_Margin__c") { ApplicationArea = All; Caption = 'Net Margin'; }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(OpenInExcel)
            {
                ApplicationArea = All;
                Caption = 'Open in Excel';
                ToolTip = 'Export the filtered data to Excel for analysis';
                Image = Excel;
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    // This will use BC's built-in Excel export functionality
                    Message('Use the "Open in Excel" action from the Share menu for seamless Excel export.');
                end;
            }
        }
    }

    trigger OnOpenPage()
    var
        StartDate: Date;
        Today: Date;
        LastSat: Date;
    begin
        // Filter for closed Won opportunities
        Rec.SetRange("Status", Rec.Status::Won);

        // Calculate last Saturday (2 days before the start of current week)
        LastSat := CalcDate('<-2D>', CalcDate('<-CW>', WorkDate()));

        // Set start date to last Friday
        StartDate := LastSat;

        // Set end date to today
        Today := WorkDate();

        // Filter for Date Closed between start date and today
        Rec.SetRange("Date Closed", StartDate, Today);
    end;
}

// Weekly Created Opportunities List
page 50104 "Weekly Created Opportunities"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = Opportunity;
    Caption = 'Weekly Created Opportunities';
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("No."; Rec."No.") { ApplicationArea = All; }
                field(Description; Rec.Description) { ApplicationArea = All; }
                field(Type_of_Project_Multi; Rec.Type_of_Project_Multi__c) { ApplicationArea = All; Caption = 'Type of Opportunity'; }
                field(Type_Of_Support_Multi; Rec.Type_Of_Support_Multi__c) { ApplicationArea = All; Caption = 'Type of Support'; }
                field("Salesperson Code"; Rec."Salesperson Code") { ApplicationArea = All; }
                field(Type_of_Technology; Rec.Type_of_Technology__c) { ApplicationArea = All; Caption = 'Type of Technology'; }
                field("Contact Name"; Rec."Contact Name") { ApplicationArea = All; Caption = 'Client Name'; }
                field(Partner_For_The_Project; Rec.Partner_For_The_Project__c) { ApplicationArea = All; Caption = 'Partner'; }
                field("Sales Cycle Code"; Rec."Sales Cycle Code") { ApplicationArea = All; }
                field("Current Sales Cycle Stage"; Rec."Current Sales Cycle Stage") { ApplicationArea = All; }
                field(Stage_Description; GetStageDescription()) { ApplicationArea = All; Caption = 'Stage Description'; }
                field("Creation Date"; Rec."Creation Date") { ApplicationArea = All; }
                field("Estimated Closing Date"; Rec."Estimated Closing Date") { ApplicationArea = All; }
                field("Date Closed"; Rec."Date Closed") { ApplicationArea = All; }
                field(Amount; Rec.Amount) { ApplicationArea = All; }
                field(Margin_Percent; Rec."Margin_Percent__c") { ApplicationArea = All; Caption = 'Margin on the sale (%)'; }
                field(Net_Margin; Rec."Net_Margin__c") { ApplicationArea = All; Caption = 'Net Margin'; }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(OpenInExcel)
            {
                ApplicationArea = All;
                Caption = 'Open in Excel';
                ToolTip = 'Export the filtered data to Excel for analysis';
                Image = Excel;
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    // This will use BC's built-in Excel export functionality
                    Message('Use the "Open in Excel" action from the Share menu for seamless Excel export.');
                end;
            }
        }
    }

    trigger OnOpenPage()
    var
        StartDate: Date;
        Today: Date;
        LastSat: Date;
    begin
        // Calculate last Saturday (2 days before the start of current week)
        LastSat := CalcDate('<-2D>', CalcDate('<-CW>', WorkDate()));

        // Set start date to last Saturday
        StartDate := LastSat;

        // Set end date to today
        Today := WorkDate();

        // Filter for Creation Date between start date and today
        Rec.SetRange("Creation Date", StartDate, Today);
    end;

    local procedure GetStageDescription(): Text[100]
    var
        SalesCycleStage: Record "Sales Cycle Stage";
    begin
        if SalesCycleStage.Get(Rec."Sales Cycle Code", Rec."Current Sales Cycle Stage") then
            exit(SalesCycleStage.Description);
        exit('');
    end;
}

// Open Opportunities List
page 50103 "Open Opportunities"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = Opportunity;
    Caption = 'Open Opportunities';
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("No."; Rec."No.") { ApplicationArea = All; }
                field(Description; Rec.Description) { ApplicationArea = All; }
                field(Type_of_Project_Multi; Rec.Type_of_Project_Multi__c) { ApplicationArea = All; Caption = 'Type of Opportunity'; }
                field(Type_Of_Support_Multi; Rec.Type_Of_Support_Multi__c) { ApplicationArea = All; Caption = 'Type of Support'; }
                field("Salesperson Code"; Rec."Salesperson Code") { ApplicationArea = All; }
                field(Type_of_Technology; Rec.Type_of_Technology__c) { ApplicationArea = All; Caption = 'Type of Technology'; }
                field("Contact Name"; Rec."Contact Name") { ApplicationArea = All; Caption = 'Client Name'; }
                field(Partner_For_The_Project; Rec.Partner_For_The_Project__c) { ApplicationArea = All; Caption = 'Partner'; }
                field("Sales Cycle Code"; Rec."Sales Cycle Code") { ApplicationArea = All; }
                field("Current Sales Cycle Stage"; Rec."Current Sales Cycle Stage") { ApplicationArea = All; }
                field(Stage_Description; GetStageDescription()) { ApplicationArea = All; Caption = 'Stage Description'; }
                field("Creation Date"; Rec."Creation Date") { ApplicationArea = All; }
                field("Estimated Closing Date"; Rec."Estimated Closing Date") { ApplicationArea = All; }
                field("Date Closed"; Rec."Date Closed") { ApplicationArea = All; }
                field(Amount; Rec.Amount) { ApplicationArea = All; }
                field(Margin_Percent; Rec."Margin_Percent__c") { ApplicationArea = All; Caption = 'Margin on the sale (%)'; }
                field(Net_Margin; Rec."Net_Margin__c") { ApplicationArea = All; Caption = 'Net Margin'; }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(OpenInExcel)
            {
                ApplicationArea = All;
                Caption = 'Open in Excel';
                ToolTip = 'Export the filtered data to Excel for analysis';
                Image = Excel;
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;

                trigger OnAction()
                begin
                    // This will use BC's built-in Excel export functionality
                    Message('Use the "Open in Excel" action from the Share menu for seamless Excel export.');
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        // Filter for non-closed opportunities
        Rec.SetRange("Closed", false);
    end;

    local procedure GetStageDescription(): Text[100]
    var
        SalesCycleStage: Record "Sales Cycle Stage";
    begin
        if SalesCycleStage.Get(Rec."Sales Cycle Code", Rec."Current Sales Cycle Stage") then
            exit(SalesCycleStage.Description);
        exit('');
    end;
}