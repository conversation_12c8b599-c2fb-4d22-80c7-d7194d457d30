page 50142 "ISR Opportunity KPI"
{
    ApplicationArea = All;
    Caption = 'My Opportunity Metrics';
    PageType = CardPart;
    SourceTable = Opportunity;

    layout
    {
        area(Content)
        {
            cuegroup("Activities")
            {
                CuegroupLayout = Wide;
                field("Total Opportunities"; TotalOpportunities)
                {
                    ApplicationArea = All;
                    Caption = 'Total Opportunities';
                    ToolTip = 'Shows the total number of opportunities assigned to you';
                    DrillDownPageId = "ISR Opportunity List Page";
                    Style = Strong;
                }
                field("Total Value"; TotalValue)
                {
                    ApplicationArea = All;
                    Caption = 'Total Pipeline Value';
                    ToolTip = 'Shows the total value of all opportunities';
                    Style = Favorable;
                }
            }
            cuegroup("Opportunity Status")
            {
                field("Open Opportunities"; OpenOpportunities)
                {
                    ApplicationArea = All;
                    Caption = 'Open';
                    ToolTip = 'Shows the number of open opportunities';
                    Style = Ambiguous;
                }
                field("Won Opportunities"; WonOpportunities)
                {
                    ApplicationArea = All;
                    Caption = 'Won';
                    ToolTip = 'Shows the number of won opportunities';
                    Style = Favorable;
                }
                field("Lost Opportunities"; LostOpportunities)
                {
                    ApplicationArea = All;
                    Caption = 'Lost';
                    ToolTip = 'Shows the number of lost opportunities';
                    Style = Unfavorable;
                }
                field("Win Rate"; WinRate)
                {
                    ApplicationArea = All;
                    Caption = 'Win Rate %';
                    ToolTip = 'Shows the win rate percentage';
                    Style = Strong;
                }
            }
            cuegroup("Financial Metrics")
            {
                field("Total Margin"; TotalMargin)
                {
                    ApplicationArea = All;
                    Caption = 'Total Margin';
                    ToolTip = 'Shows the total margin amount';
                    Style = Favorable;
                }
                field("Average Deal Size"; AverageDealSize)
                {
                    ApplicationArea = All;
                    Caption = 'Avg Deal Size';
                    ToolTip = 'Shows the average opportunity value';
                    Style = Strong;
                }
            }
        }
    }

    var
        TotalOpportunities: Integer;
        OpenOpportunities: Integer;
        WonOpportunities: Integer;
        LostOpportunities: Integer;
        WinRate: Decimal;
        TotalValue: Decimal;
        TotalMargin: Decimal;
        AverageDealSize: Decimal;

    trigger OnAfterGetCurrRecord()
    begin
        CalculateKPIs();
    end;

    trigger OnOpenPage()
    begin
        CalculateKPIs();
    end;

    local procedure CalculateKPIs()
    var
        Opportunity: Record Opportunity;
        UserSetup: Record "User Setup";
        SalespersonCode: Code[20];
    begin
        // Get current user's salesperson code
        if UserSetup.Get(UserId) then
            SalespersonCode := UserSetup."Salespers./Purch. Code";

        if SalespersonCode = '' then
            exit;

        // Calculate metrics
        TotalOpportunities := 0;
        OpenOpportunities := 0;
        WonOpportunities := 0;
        LostOpportunities := 0;
        WinRate := 0;
        TotalValue := 0;
        TotalMargin := 0;
        AverageDealSize := 0;

        Opportunity.SetRange("Salesperson Code", SalespersonCode);
        if Opportunity.FindSet() then begin
            repeat
                TotalOpportunities += 1;
                TotalValue += Opportunity."Amount";
                TotalMargin += Opportunity."Amount" - Opportunity."Cost__c";

                case Opportunity.Status of
                    Opportunity.Status::"In Progress",
                    Opportunity.Status::"Not Started":
                        OpenOpportunities += 1;
                    Opportunity.Status::Won:
                        WonOpportunities += 1;
                    Opportunity.Status::Lost:
                        LostOpportunities += 1;
                end;
            until Opportunity.Next() = 0;
        end;

        // Calculate derived metrics
        if TotalOpportunities > 0 then begin
            AverageDealSize := TotalValue / TotalOpportunities;
            if (WonOpportunities + LostOpportunities) > 0 then
                WinRate := Round((WonOpportunities / (WonOpportunities + LostOpportunities)) * 100, 0.1);
        end;
    end;
}