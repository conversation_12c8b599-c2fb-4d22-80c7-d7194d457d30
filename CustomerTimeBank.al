table 50100 "Customer Time Bank"
{
    Caption = 'Customer Time Bank';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Account__c"; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AutoIncrement = true;
        }
        field(3; "Name"; Text[50])
        {
            Caption = 'Prepaid Hours Name';
            // Allow editing during import but not during normal operations
            // Will be controlled by page permissions instead
        }
        field(4; "ERP_Project_Name__c"; Text[255])
        {
            Caption = 'ERP Project Name';
        }
        field(5; "Prepaid_Type__c"; Option)
        {
            Caption = 'Prepaid Type';
            OptionMembers = "None","Hours Purchased","Preauthorized Hours";
        }
        field(6; "Price_Before_Tax__c"; Decimal)
        {
            Caption = 'Price Before Tax';
            DecimalPlaces = 2 : 2;

            trigger OnValidate()
            begin
                if "Price_Before_Tax__c" < "Remaining_Dollar_Amount__c" then
                    Error('Price Before Tax cannot be less than Remaining Dollar Amount.');
            end;
        }
        field(7; "Hours_Sold__c"; Decimal)
        {
            Caption = 'Hours Sold';
            DecimalPlaces = 2 : 2;

            trigger OnValidate()
            begin
                if "Hours_Sold__c" < "Remaining_Hours__c" then
                    Error('Hours Sold cannot be less than Remaining Hours.');
            end;
        }
        field(8; "Remaining_Hours__c"; Decimal)
        {
            Caption = 'Remaining Hours';
            DecimalPlaces = 2 : 2;

            trigger OnValidate()
            begin
                if "Remaining_Hours__c" > "Hours_Sold__c" then
                    Error('Remaining Hours cannot exceed Hours Sold.');
                if "Remaining_Hours__c" < 0 then
                    Error('Remaining Hours cannot be negative.');
            end;
        }
        field(9; "Remaining_Dollar_Amount__c"; Decimal)
        {
            Caption = 'Remaining Dollar Amount';
            DecimalPlaces = 2 : 2;

            trigger OnValidate()
            begin
                if "Remaining_Dollar_Amount__c" > "Price_Before_Tax__c" then
                    Error('Remaining Dollar Amount cannot exceed Price Before Tax.');
                if "Remaining_Dollar_Amount__c" < 0 then
                    Error('Remaining Dollar Amount cannot be negative.');
            end;
        }
        field(10; "Start_Date__c"; Date)
        {
            Caption = 'Time Bank Start Date';
            NotBlank = true;
        }
        field(11; "End_Date__c"; Date)
        {
            Caption = 'Time Bank End Date';
            NotBlank = true;
        }
        field(12; "Is_Consumed__c"; Boolean)
        {
            Caption = 'Is Consumed';
        }
        field(13; "Is_Imported"; Boolean)
        {
            Caption = 'Is Imported Record';
            Editable = false;
        }
        field(14; "Customer_Name"; Text[100])
        {
            Caption = 'Customer Name';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup(Customer.Name where("No." = field("Account__c")));
        }
        field(15; "Salesperson_Code"; Code[20])
        {
            Caption = 'Salesperson Code';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup(Customer."Salesperson Code" where("No." = field("Account__c")));
        }
        field(16; "Salesforce_Account_Id"; Text[50])
        {
            Caption = 'Salesforce Account Id';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup(Contact.SalesforceAccountId where("No." = field("Contact_No_From_Customer")));
        }
        field(17; "Contact_No_From_Customer"; Code[20])
        {
            Caption = 'Contact No From Customer';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("Contact Business Relation"."Contact No." where("No." = field("Account__c"), "Business Relation Code" = const('CUST')));
        }
    }

    keys
    {
        key(PK; "Account__c", "Line No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        CustomerTimeBank: Record "Customer Time Bank";
        LastNumberText: Text;
        LastNumber: Integer;
        MaxNumber: Integer;
        CurrentNumber: Integer;
    begin
        // If the Name field is already populated (during import), just validate it
        if "Name" <> '' then begin
            // Mark as imported record
            "Is_Imported" := true;

            // Validate the format
            if StrLen("Name") = 8 then begin
                if CopyStr("Name", 1, 4) <> 'PPH-' then
                    Error('Imported Prepaid Hours Name must start with PPH-');

                if not Evaluate(CurrentNumber, CopyStr("Name", 5, 4)) then
                    Error('Imported Prepaid Hours Name must end with a 4-digit number');
            end else
                Error('Imported Prepaid Hours Name must be in format PPH-XXXX');
        end
        else begin
            // For new records, find the highest number used
            MaxNumber := 100; // Start at 100 by default

            CustomerTimeBank.Reset();
            if CustomerTimeBank.FindSet() then
                repeat
                    LastNumberText := CopyStr(CustomerTimeBank."Name", 5, 4);
                    if Evaluate(LastNumber, LastNumberText) then
                        if LastNumber > MaxNumber then
                            MaxNumber := LastNumber;
                until CustomerTimeBank.Next() = 0;

            // Increment to the next number
            MaxNumber += 1;

            // Format and set the name
            "Name" := StrSubstNo('PPH-%1', Format(MaxNumber, 4, '<Integer,4><Filler,0>'));
        end;

        Validate("Remaining_Hours__c");
        Validate("Remaining_Dollar_Amount__c");
    end;

    trigger OnModify()
    begin
        Validate("Remaining_Hours__c");
        Validate("Remaining_Dollar_Amount__c");
    end;
}

page 50100 "Customer Time Bank Subform"
{
    PageType = ListPart;
    SourceTable = "Customer Time Bank";
    Caption = 'Bank Hours Lines';

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("Name"; Rec."Name")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
                field("ERP_Project_Name__c"; Rec."ERP_Project_Name__c")
                {
                    ApplicationArea = All;
                }
                field("Prepaid_Type__c"; Rec."Prepaid_Type__c")
                {
                    ApplicationArea = All;
                }
                field("Price_Before_Tax__c"; Rec."Price_Before_Tax__c")
                {
                    ApplicationArea = All;
                }
                field("Remaining_Dollar_Amount__c"; Rec."Remaining_Dollar_Amount__c")
                {
                    ApplicationArea = All;
                }
                field("Hours_Sold__c"; Rec."Hours_Sold__c")
                {
                    ApplicationArea = All;
                }
                field("Remaining_Hours__c"; Rec."Remaining_Hours__c")
                {
                    ApplicationArea = All;
                }
                field("Start_Date__c"; Rec."Start_Date__c")
                {
                    ApplicationArea = All;
                }
                field("End_Date__c"; Rec."End_Date__c")
                {
                    ApplicationArea = All;
                }
                field("Is_Consumed__c"; Rec."Is_Consumed__c")
                {
                    ApplicationArea = All;
                }
                field("Is_Imported"; Rec."Is_Imported")
                {
                    ApplicationArea = All;
                    Visible = false; // Hide this field in the UI
                }
                field("Customer_Name"; Rec."Customer_Name")
                {
                    ApplicationArea = All;
                    Visible = false;
                }
                field("Salesperson_Code"; Rec."Salesperson_Code")
                {
                    ApplicationArea = All;
                    Visible = false;
                }
                field("Salesforce_Account_Id"; Rec."Salesforce_Account_Id")
                {
                    ApplicationArea = All;
                    Visible = false;
                }
            }
        }
    }
}

// Create an Import Page for Salesforce data
page 50102 "Time Bank Import"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Customer Time Bank";
    Caption = 'Import Time Bank Records';
    InsertAllowed = true;
    ModifyAllowed = true;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            group(ImportFields)
            {
                Caption = 'Import Time Bank Record';

                field("Account__c"; Rec."Account__c")
                {
                    ApplicationArea = All;
                    TableRelation = Customer;
                }
                field("Name"; Rec."Name")
                {
                    ApplicationArea = All;
                    Editable = true; // Allow editing during import
                }
                field("ERP_Project_Name__c"; Rec."ERP_Project_Name__c")
                {
                    ApplicationArea = All;
                }
                field("Prepaid_Type__c"; Rec."Prepaid_Type__c")
                {
                    ApplicationArea = All;
                }
                field("Price_Before_Tax__c"; Rec."Price_Before_Tax__c")
                {
                    ApplicationArea = All;
                }
                field("Remaining_Dollar_Amount__c"; Rec."Remaining_Dollar_Amount__c")
                {
                    ApplicationArea = All;
                }
                field("Hours_Sold__c"; Rec."Hours_Sold__c")
                {
                    ApplicationArea = All;
                }
                field("Remaining_Hours__c"; Rec."Remaining_Hours__c")
                {
                    ApplicationArea = All;
                }
                field("Start_Date__c"; Rec."Start_Date__c")
                {
                    ApplicationArea = All;
                }
                field("End_Date__c"; Rec."End_Date__c")
                {
                    ApplicationArea = All;
                }
                field("Is_Consumed__c"; Rec."Is_Consumed__c")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ImportRecord)
            {
                ApplicationArea = All;
                Caption = 'Save Imported Record';
                Image = ImportExcel;

                trigger OnAction()
                begin
                    if Rec."Name" = '' then
                        Error('Prepaid Hours Name is required');

                    if not Rec.Insert(true) then
                        Error('Failed to insert record');

                    Message('Record imported successfully');
                    CurrPage.Close();
                end;
            }
        }
    }
}

pageextension 50101 "Customer Card Ext" extends "Customer Card"
{
    layout
    {
        addafter("Address & Contact")
        {
            group("Time Bank")
            {
                Caption = 'Bank Hours';
                part(TimeBankLines; "Customer Time Bank Subform")
                {
                    ApplicationArea = All;
                    SubPageLink = "Account__c" = field("No.");
                }
            }
        }
    }

    actions
    {
        addlast(processing)
        {
            action(ImportTimeBank)
            {
                ApplicationArea = All;
                Caption = 'Import Time Bank Record';
                Image = Import;

                trigger OnAction()
                var
                    TimeBankImport: Page "Time Bank Import";
                    CustomerTimeBank: Record "Customer Time Bank";
                begin
                    CustomerTimeBank.Init();
                    CustomerTimeBank."Account__c" := Rec."No.";

                    TimeBankImport.SetRecord(CustomerTimeBank);
                    TimeBankImport.RunModal();
                end;
            }
        }
    }
}