// Page to view mass email recipients and their status
page 50116 "Mass Email Recipients"
{
    PageType = List;
    ApplicationArea = All;
    Caption = 'Mass Email Recipients';
    SourceTable = "Mass Email Recipients";
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(Recipients)
            {
                field("Line No."; Rec."Line No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Line number for this recipient.';
                }
                field("Contact No."; Rec."Contact No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Contact number for this recipient.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Name of the contact.';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ToolTip = 'Email address of the recipient.';
                }
                field("Company Name"; Rec."Company Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Company name for this contact.';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Salesperson associated with this contact.';
                }
                field("Send Status"; Rec."Send Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Current send status for this recipient.';
                    StyleExpr = SendStatusStyleExpr;
                }
                field("Sent Date Time"; Rec."Sent Date Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Date and time when the email was sent to this recipient.';
                }
                field("Personalized Subject"; Rec."Personalized Subject")
                {
                    ApplicationArea = All;
                    ToolTip = 'Personalized subject line sent to this recipient.';
                }
                field("Error Message"; Rec."Error Message")
                {
                    ApplicationArea = All;
                    ToolTip = 'Error message if the email failed to send.';
                    Style = Unfavorable;
                }
                field("Retry Count"; Rec."Retry Count")
                {
                    ApplicationArea = All;
                    ToolTip = 'Number of retry attempts for this recipient.';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ViewContact)
            {
                ApplicationArea = All;
                Caption = 'View Contact';
                Image = ContactPerson;
                ToolTip = 'View the contact card for this recipient.';
                Enabled = HasContactNo;

                trigger OnAction()
                begin
                    ViewContactCard();
                end;
            }

            action(ViewLogs)
            {
                ApplicationArea = All;
                Caption = 'View Logs';
                Image = Log;
                ToolTip = 'View detailed logs for this recipient.';

                trigger OnAction()
                begin
                    ViewRecipientLogs();
                end;
            }

            action(FilterBySent)
            {
                ApplicationArea = All;
                Caption = 'Show Sent Only';
                Image = FilterLines;
                ToolTip = 'Filter to show only successfully sent emails.';

                trigger OnAction()
                begin
                    Rec.SetRange("Send Status", Rec."Send Status"::Sent);
                    CurrPage.Update(false);
                end;
            }

            action(FilterByFailed)
            {
                ApplicationArea = All;
                Caption = 'Show Failed Only';
                Image = FilterLines;
                ToolTip = 'Filter to show only failed emails.';

                trigger OnAction()
                begin
                    Rec.SetRange("Send Status", Rec."Send Status"::Failed);
                    CurrPage.Update(false);
                end;
            }

            action(FilterByPending)
            {
                ApplicationArea = All;
                Caption = 'Show Pending Only';
                Image = FilterLines;
                ToolTip = 'Filter to show only pending emails.';

                trigger OnAction()
                begin
                    Rec.SetRange("Send Status", Rec."Send Status"::Pending);
                    CurrPage.Update(false);
                end;
            }

            action(ClearFilters)
            {
                ApplicationArea = All;
                Caption = 'Clear Filters';
                Image = ClearFilter;
                ToolTip = 'Remove all filters and show all recipients.';

                trigger OnAction()
                begin
                    Rec.Reset();
                    CurrPage.Update(false);
                end;
            }

            action(ExportRecipients)
            {
                ApplicationArea = All;
                Caption = 'Export to Excel';
                Image = ExportToExcel;
                ToolTip = 'Export the recipient list to Excel.';

                trigger OnAction()
                begin
                    ExportToExcel();
                end;
            }
        }

        area(Promoted)
        {
            group(Filters)
            {
                Caption = 'Filters';
                actionref(FilterBySentRef; FilterBySent)
                {
                }
                actionref(FilterByFailedRef; FilterByFailed)
                {
                }
                actionref(FilterByPendingRef; FilterByPending)
                {
                }
                actionref(ClearFiltersRef; ClearFilters)
                {
                }
            }

            group(Actions)
            {
                Caption = 'Actions';
                actionref(ViewContactRef; ViewContact)
                {
                }
                actionref(ViewLogsRef; ViewLogs)
                {
                }
                actionref(ExportRecipientsRef; ExportRecipients)
                {
                }
            }
        }
    }

    var
        SendStatusStyleExpr: Text;
        HasContactNo: Boolean;

    trigger OnAfterGetRecord()
    begin
        SetStyleExpressions();
        SetActionVisibility();
    end;

    local procedure SetStyleExpressions()
    begin
        case Rec."Send Status" of
            Rec."Send Status"::Sent:
                SendStatusStyleExpr := 'Favorable';
            Rec."Send Status"::Failed:
                SendStatusStyleExpr := 'Unfavorable';
            Rec."Send Status"::Skipped:
                SendStatusStyleExpr := 'Subordinate';
            else
                SendStatusStyleExpr := 'Standard';
        end;
    end;

    local procedure SetActionVisibility()
    begin
        HasContactNo := Rec."Contact No." <> '';
    end;

    local procedure ViewContactCard()
    var
        Contact: Record Contact;
        ContactCard: Page "Contact Card";
    begin
        if Rec."Contact No." = '' then begin
            Message('No contact number associated with this recipient.');
            exit;
        end;

        if Contact.Get(Rec."Contact No.") then begin
            ContactCard.SetRecord(Contact);
            ContactCard.Run();
        end else
            Message('Contact %1 not found.', Rec."Contact No.");
    end;

    local procedure ViewRecipientLogs()
    var
        MassEmailLog: Record "Mass Email Log";
        MassEmailLogsPage: Page "Mass Email Logs";
    begin
        MassEmailLog.SetRange("Job ID", Rec."Job ID");
        MassEmailLog.SetRange("Recipient Line No.", Rec."Line No.");
        MassEmailLogsPage.SetTableView(MassEmailLog);
        MassEmailLogsPage.Run();
    end;

    local procedure ExportToExcel()
    var
        TempExcelBuffer: Record "Excel Buffer" temporary;
    begin
        TempExcelBuffer.DeleteAll();

        // Add headers
        TempExcelBuffer.NewRow();
        TempExcelBuffer.AddColumn('Line No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Contact No.', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Contact Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('E-Mail', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Company Name', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Send Status', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Sent Date Time', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);
        TempExcelBuffer.AddColumn('Error Message', false, '', true, false, false, '', TempExcelBuffer."Cell Type"::Text);

        // Add data
        if Rec.FindSet() then
            repeat
                TempExcelBuffer.NewRow();
                TempExcelBuffer.AddColumn(Format(Rec."Line No."), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Number);
                TempExcelBuffer.AddColumn(Rec."Contact No.", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."Contact Name", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."E-Mail", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."Company Name", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Rec."Send Status"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Format(Rec."Sent Date Time"), false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
                TempExcelBuffer.AddColumn(Rec."Error Message", false, '', false, false, false, '', TempExcelBuffer."Cell Type"::Text);
            until Rec.Next() = 0;

        TempExcelBuffer.CreateNewBook('Mass Email Recipients');
        TempExcelBuffer.WriteSheet('Recipients', CompanyName(), UserId());
        TempExcelBuffer.CloseBook();
        TempExcelBuffer.SetFriendlyFilename('MassEmailRecipients');
        TempExcelBuffer.OpenExcel();
    end;
}
