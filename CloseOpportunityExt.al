pageextension 50107 "Close Opportunity Ext" extends "Close Opportunity" // Page 5128
{
    layout
    {
        addafter("Close Opportunity Code")
        {
            field("Estimated Delivery Date"; EstimatedDeliveryDate)
            {
                ApplicationArea = All;
                Caption = 'Estimated Delivery Date';
                ToolTip = 'Specifies the estimated delivery date for the opportunity. This field is mandatory when the opportunity is won.';
                ShowMandatory = IsWonSelected;

                trigger OnValidate()
                var
                    Opportunity: Record Opportunity;
                begin
                    if Opportunity.Get(Rec."Opportunity No.") then begin
                        Opportunity."Estimated Delivery Date" := EstimatedDeliveryDate;
                        Opportunity.Modify();
                    end;
                end;
            }
        }
    }

    var
        IsWonSelected: Boolean;
        EstimatedDeliveryDate: Date;

    trigger OnAfterGetRecord()
    begin
        LoadEstimatedDeliveryDate();
        UpdateMandatoryField();
    end;

    trigger OnAfterGetCurrRecord()
    begin
        LoadEstimatedDeliveryDate();
        UpdateMandatoryField();
    end;

    local procedure LoadEstimatedDeliveryDate()
    var
        Opportunity: Record Opportunity;
    begin
        EstimatedDeliveryDate := 0D;
        if Opportunity.Get(Rec."Opportunity No.") then
            EstimatedDeliveryDate := Opportunity."Estimated Delivery Date";
    end;

    local procedure UpdateMandatoryField()
    begin
        IsWonSelected := Rec."Action Taken" = Rec."Action Taken"::Won;
        CurrPage.Update(false);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        Opportunity: Record Opportunity;
    begin
        if CloseAction in [ACTION::OK, ACTION::LookupOK] then begin
            // Save the estimated delivery date to the opportunity record
            if Opportunity.Get(Rec."Opportunity No.") then begin
                Opportunity."Estimated Delivery Date" := EstimatedDeliveryDate;
                Opportunity.Modify();
            end;

            // Check if Won is selected and Estimated Delivery Date is mandatory
            if (Rec."Action Taken" = Rec."Action Taken"::Won) and (EstimatedDeliveryDate = 0D) then
                Error('Estimated Delivery Date is mandatory when the opportunity is won.');
        end;
        exit(true);
    end;
}