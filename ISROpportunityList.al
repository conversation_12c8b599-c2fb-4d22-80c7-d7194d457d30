page 50141 "ISR Opportunity List"
{
    ApplicationArea = All;
    Caption = 'My Opportunities';
    PageType = ListPart;
    SourceTable = Opportunity;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity number';

                    trigger OnDrillDown()
                    begin
                        Page.Run(Page::"Opportunity Card", Rec);
                    end;
                }
                field("Creation Date"; Rec."Creation Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies when the opportunity was created';
                }
                field(Description; Rec.Description)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity description';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name';
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity status';
                }
                field("Amount"; Rec."Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the opportunity amount';
                }
                field("Type_of_Project_Multi__c"; Rec."Type_of_Project_Multi__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the type of opportunity';
                }
                field("Partner_For_The_Project__c"; Rec."Partner_For_The_Project__c")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the partner for the project';
                }
                field("Estimated Closing Date"; Rec."Estimated Closing Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the estimated closing date';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Open Card")
            {
                ApplicationArea = All;
                Caption = 'Open';
                Image = Edit;
                RunObject = page "Opportunity Card";
                RunPageLink = "No." = field("No.");
                ToolTip = 'Open the opportunity card';
            }
        }
    }

    trigger OnOpenPage()
    var
        UserSetup: Record "User Setup";
        SalespersonCode: Code[20];
    begin
        // Filter opportunities by current user's salesperson code
        if UserSetup.Get(UserId) then
            SalespersonCode := UserSetup."Salespers./Purch. Code";

        if SalespersonCode <> '' then begin
            Rec.FilterGroup(2);
            Rec.SetRange("Salesperson Code", SalespersonCode);
            Rec.FilterGroup(0);
        end;

        // Sort by newest first (descending order)
        Rec.SetCurrentKey("Creation Date");
        Rec.Ascending(false);
    end;
}