permissionset 50100 "Time Bank Access"
{
    Caption = 'Customer Time Bank Access';
    Assignable = true;

    Permissions =
        tabledata "Customer Time Bank" = RIMD,
        table "Customer Time Bank" = X,
        page "Customer Time Bank Subform" = X;
}

permissionset 50101 "Tech Type Access"
{
    Caption = 'Tech Type Access';
    Assignable = true;

    Permissions =
        tabledata "Technology Type" = RIMD,
        table "Technology Type" = X,
        page "Technology Type Selection" = X;
}

permissionset 50102 "Support Type Access"
{
    Caption = 'Support Type Access';
    Assignable = true;

    Permissions =
        tabledata "Support Type" = RIMD,
        table "Support Type" = X,
        page "Support Type Selection" = X;
}

permissionset 50110 "Mass Email Access"
{
    Caption = 'Mass Email Access';
    Assignable = true;

    Permissions =
        tabledata "Mass Email Contact Buffer" = RIMD,
        table "Mass Email Contact Buffer" = X,
        page "Mass Email Send" = X,
        codeunit "Mass Email Management" = X;
}

permissionset 50105 "Contact SP Update"
{
    Caption = 'Contact Salesperson Update Access';
    Assignable = true;

    Permissions =
        tabledata "Contact Update Buffer" = RIMD,
        table "Contact Update Buffer" = X,
        page "Contact Salesperson Update" = X,
        codeunit "Contact Salesperson Update Mgt" = X;
}

permissionset 50103 "Opp Type Access"
{
    Caption = 'Opportunity Type Access';
    Assignable = true;

    Permissions =
        tabledata "Opportunity Type" = RIMD,
        table "Opportunity Type" = X,
        page "Opportunity Type Selection" = X;
}

permissionset 50104 "Opp Migration Access"
{
    Caption = 'Opportunity Migration Access';
    Assignable = true;

    Permissions =
        codeunit "Opportunity Migration" = X,
        page "Migrate Opportunity Fields" = X;
}

permissionset 50106 "Mass Import Access"
{
    Caption = 'Mass Interaction Import Access';
    Assignable = true;

    Permissions =
        tabledata "Mass Interaction Import Result" = RIMD,
        table "Mass Interaction Import Result" = X,
        page "Mass Interaction Import" = X,
        page "Mass Interaction Import Result" = X,
        codeunit "Mass Interaction Import Mgt" = X;
}