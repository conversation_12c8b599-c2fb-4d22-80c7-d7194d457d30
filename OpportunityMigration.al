codeunit 50102 "Opportunity Migration"
{
    trigger OnRun()
    begin
        MigrateAllOpportunities();
    end;

    procedure MigrateAllOpportunities()
    var
        OpportunityRec: Record Opportunity;
        Counter: Integer;
        UpdatedCounter: Integer;
        ConfirmMsg: Label 'This will migrate existing values to multi-selection fields for all opportunities:\• Type of Opportunity → Type of Opportunity (Multi)\• Type of Support → Type of Support (Multi)\• Technician Group → Type of Technology\The original fields will remain unchanged.\Do you want to continue?';
    begin
        if not Confirm(ConfirmMsg) then
            exit;

        Counter := 0;
        UpdatedCounter := 0;
        OpportunityRec.Reset();
        if OpportunityRec.FindSet(true) then
            repeat
                Counter += 1;

                // Migrate Type of Opportunity if not already migrated and has a value
                if (OpportunityRec."Type_of_Project_Multi__c" = '') and
                   (OpportunityRec."Type_of_Project__c" <> OpportunityRec."Type_of_Project__c"::"None") then begin
                    case OpportunityRec."Type_of_Project__c" of
                        OpportunityRec."Type_of_Project__c"::"Hardware with support":
                            OpportunityRec."Type_of_Project_Multi__c" := 'Hardware with support';
                        OpportunityRec."Type_of_Project__c"::"Hardware without support":
                            OpportunityRec."Type_of_Project_Multi__c" := 'Hardware without support';
                        OpportunityRec."Type_of_Project__c"::"Hardware + services":
                            OpportunityRec."Type_of_Project_Multi__c" := 'Hardware + services';
                        OpportunityRec."Type_of_Project__c"::Service:
                            OpportunityRec."Type_of_Project_Multi__c" := 'Service';
                        OpportunityRec."Type_of_Project__c"::"Managed Service":
                            OpportunityRec."Type_of_Project_Multi__c" := 'Managed Service';
                        OpportunityRec."Type_of_Project__c"::Software:
                            OpportunityRec."Type_of_Project_Multi__c" := 'Software';
                        OpportunityRec."Type_of_Project__c"::"Tender Offer":
                            OpportunityRec."Type_of_Project_Multi__c" := 'Tender Offer';
                    end;
                end;

                // Migrate Type of Support if not already migrated and has a value
                if (OpportunityRec."Type_Of_Support_Multi__c" = '') and
                   (OpportunityRec."Type_Of_Support__c" <> OpportunityRec."Type_Of_Support__c"::"None") then begin
                    case OpportunityRec."Type_Of_Support__c" of
                        OpportunityRec."Type_Of_Support__c"::Installation:
                            OpportunityRec."Type_Of_Support_Multi__c" := 'Installation';
                        OpportunityRec."Type_Of_Support__c"::Renewal:
                            OpportunityRec."Type_Of_Support_Multi__c" := 'Renewal';
                        OpportunityRec."Type_Of_Support__c"::"New license":
                            OpportunityRec."Type_Of_Support_Multi__c" := 'New license';
                        OpportunityRec."Type_Of_Support__c"::"New Managed Service":
                            OpportunityRec."Type_Of_Support_Multi__c" := 'New Managed Service';
                        OpportunityRec."Type_Of_Support__c"::Other:
                            OpportunityRec."Type_Of_Support_Multi__c" := 'Other';
                    end;
                end;

                // Migrate Technology Type from Technician Group if not already migrated and has a value
                if (OpportunityRec."Type_of_Technology__c" = '') and
                   (OpportunityRec."Technician_Group__c" <> OpportunityRec."Technician_Group__c"::"None") then begin
                    case OpportunityRec."Technician_Group__c" of
                        OpportunityRec."Technician_Group__c"::"Tech Network":
                            OpportunityRec."Type_of_Technology__c" := 'Network';
                        OpportunityRec."Technician_Group__c"::"Tech System":
                            OpportunityRec."Type_of_Technology__c" := 'System Infra';
                        OpportunityRec."Technician_Group__c"::"Tech Monitoring":
                            OpportunityRec."Type_of_Technology__c" := 'Monitoring';
                        OpportunityRec."Technician_Group__c"::"Tech Cybersecurity":
                            OpportunityRec."Type_of_Technology__c" := 'Cyber Security';
                        OpportunityRec."Technician_Group__c"::"Tech RTLS":
                            OpportunityRec."Type_of_Technology__c" := 'RTLS';
                    end;
                end;

                // Only modify if we actually changed something
                if (OpportunityRec."Type_of_Project_Multi__c" <> '') or
                   (OpportunityRec."Type_Of_Support_Multi__c" <> '') or
                   (OpportunityRec."Type_of_Technology__c" <> '') then begin
                    OpportunityRec.Modify();
                    UpdatedCounter += 1;
                end;
            until OpportunityRec.Next() = 0;

        Message('Migration completed.\Total opportunities processed: %1\Updated opportunities: %2', Counter, UpdatedCounter);
    end;
}
