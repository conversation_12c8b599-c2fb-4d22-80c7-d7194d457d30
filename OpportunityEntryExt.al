tableextension 50101 OpportunityEntryExt extends "Opportunity Entry"
{
    fields
    {
        field(50101; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
            DataClassification = ToBeClassified;
        }
        field(50103; "Opportunity Name"; Text[100])
        {
            Caption = 'Opportunity Name';
            DataClassification = ToBeClassified;
        }
        field(50100; SalesforceOppHistoryId; Text[50])
        {
            Caption = 'Salesforce Opportunity History ID';
            DataClassification = ToBeClassified;
        }
        field(50104; Status; Enum "Opportunity Status")
        {
            Caption = 'Status';
            DataClassification = ToBeClassified;
        }
    }
}

pageextension 50102 OpportunityEntriesExt extends "Opportunity Entries"
{
    layout
    {
        addafter("Opportunity No.")
        {
            field("Opportunity Name"; Rec."Opportunity Name")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Opportunity Name.';
            }
            field("Contact Name"; Rec."Contact Name")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Contact Name.';
            }
            field("Salesperson Code"; Rec."Salesperson Code")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Salesperson Code.';
            }
            field(SalesforceOppHistoryId; Rec.SalesforceOppHistoryId)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Salesforce Opportunity History ID.';
            }
            field(Status; Rec.Status)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the status of the opportunity.';
            }
        }
    }

    trigger OnAfterGetRecord()
    var
        OpportunityRec: Record "Opportunity";
        ContactRec: Record "Contact";
    begin
        if Rec."Opportunity No." <> '' then begin
            Clear(OpportunityRec);
            if OpportunityRec.Get(Rec."Opportunity No.") then begin
                // Always update Status to ensure it's accurate for filtering
                Rec.Status := OpportunityRec.Status;

                // Save the Status value to the database so it can be used for filtering
                Rec.Modify();

                // Only update these fields if they're empty
                if (Rec."Opportunity Name" = '') and (Rec."Contact Name" = '') then begin
                    Rec."Opportunity Name" := OpportunityRec.Description;
                    if OpportunityRec."Contact Name" <> '' then
                        Rec."Contact Name" := OpportunityRec."Contact Name"
                    else
                        if OpportunityRec."Contact No." <> '' then begin
                            Clear(ContactRec);
                            if ContactRec.Get(OpportunityRec."Contact No.") then
                                Rec."Contact Name" := ContactRec.Name;
                        end;
                end;
            end;
        end;
    end;

    trigger OnOpenPage()
    begin
        // Update all records' Status field on page open
        UpdateAllOpportunityEntryStatus();
    end;

    local procedure UpdateAllOpportunityEntryStatus()
    var
        OpportunityEntry: Record "Opportunity Entry";
        Opportunity: Record "Opportunity";
        RecordsUpdated: Integer;
    begin
        RecordsUpdated := 0;

        if OpportunityEntry.FindSet() then
            repeat
                if (OpportunityEntry."Opportunity No." <> '') and Opportunity.Get(OpportunityEntry."Opportunity No.") then begin
                    OpportunityEntry.Status := Opportunity.Status;
                    if OpportunityEntry.Modify() then
                        RecordsUpdated += 1;
                end;
            until OpportunityEntry.Next() = 0;
    end;
}