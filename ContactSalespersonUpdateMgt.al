codeunit 50110 "Contact Salesperson Update Mgt"
{
    procedure ImportExcelFile(var ContactUpdateBuffer: Record "Contact Update Buffer"): Boolean
    var
        TempExcelBuffer: Record "Excel Buffer" temporary;
        FileInStream: InStream;
        FileName: Text;
        SheetName: Text;
        CustomerNameColumnNo: Integer;
        ImportedCount: Integer;
        RowNo: Integer;
    begin
        ContactUpdateBuffer.DeleteAll();
        ImportedCount := 0;

        // Upload file
        if not UploadIntoStream('Import Excel File', '', 'Excel Files (*.xlsx)|*.xlsx|Excel Files (*.xls)|*.xls', FileName, FileInStream) then
            exit(false);

        if FileName = '' then
            exit(false);

        // Import Excel data - simplified approach
        TempExcelBuffer.DeleteAll();

        // Try to select sheet - if it fails, use default
        SheetName := TempExcelBuffer.SelectSheetsNameStream(FileInStream);
        if SheetName = '' then
            SheetName := 'Sheet1';

        TempExcelBuffer.OpenBookStream(FileInStream, SheetName);
        TempExcelBuffer.ReadSheet();

        // Find the "Customer Name" column in the header row
        CustomerNameColumnNo := FindCustomerNameColumn(TempExcelBuffer);
        if CustomerNameColumnNo = 0 then begin
            Message('Could not find "Customer Name" column in the Excel file. Please ensure the file contains a column with this exact name.');
            exit(false);
        end;

        // Import data into buffer (skip header row)
        TempExcelBuffer.Reset();
        TempExcelBuffer.SetRange("Column No.", CustomerNameColumnNo);
        TempExcelBuffer.SetFilter("Row No.", '>1'); // Skip header row

        if TempExcelBuffer.FindSet() then begin
            repeat
                if TempExcelBuffer."Cell Value as Text" <> '' then begin
                    ContactUpdateBuffer.Init();
                    ContactUpdateBuffer."Line No." := ImportedCount + 1; // Manually set line number
                    ContactUpdateBuffer."Customer Name" := CopyStr(TempExcelBuffer."Cell Value as Text", 1, MaxStrLen(ContactUpdateBuffer."Customer Name"));
                    if ContactUpdateBuffer.Insert() then
                        ImportedCount += 1
                    else
                        Message('Failed to insert: %1 (Line No: %2)', TempExcelBuffer."Cell Value as Text", ContactUpdateBuffer."Line No.");
                end;
            until TempExcelBuffer.Next() = 0;
        end;

        if ImportedCount = 0 then begin
            Message('No valid customer names found in the Excel file.');
            exit(false);
        end;

        Message('Successfully imported %1 customer names from Excel file.', ImportedCount);
        exit(true);
    end;

    local procedure FindCustomerNameColumn(var TempExcelBuffer: Record "Excel Buffer"): Integer
    var
        CellValue: Text;
    begin
        // Look for "Customer Name" column in the first row (header)
        TempExcelBuffer.Reset();
        TempExcelBuffer.SetRange("Row No.", 1);
        if TempExcelBuffer.FindSet() then
            repeat
                CellValue := UpperCase(DelChr(TempExcelBuffer."Cell Value as Text", '=', ' '));
                if (CellValue = 'CUSTOMERNAME') or (CellValue = 'CUSTOMER NAME') or (CellValue = 'CUSTOMER_NAME') then
                    exit(TempExcelBuffer."Column No.");
            until TempExcelBuffer.Next() = 0;

        // Default to column 2 (B) if not found
        exit(2);
    end;



    procedure ProcessContactUpdates(var ContactUpdateBuffer: Record "Contact Update Buffer"; SalespersonCode: Code[20]): Text
    var
        Contact: Record Contact;
        UpdatedContacts: Integer;
        UnassignedContacts: Integer;
        NotFoundCompanies: Integer;
        ResultText: Text;
        CompanyName: Text[100];
        ProgressDialog: Dialog;
        Counter: Integer;
        TotalRecords: Integer;
    begin
        UpdatedContacts := 0;
        UnassignedContacts := 0;
        NotFoundCompanies := 0;

        // Count total records for progress
        TotalRecords := ContactUpdateBuffer.Count();

        ProgressDialog.Open('Processing contact updates...\Progress: #1######');

        // First, unassign all contacts currently assigned to this salesperson
        Contact.SetRange("Salesperson Code", SalespersonCode);
        if Contact.FindSet(true) then
            repeat
                Contact."Salesperson Code" := '';
                Contact.Modify(true);
                UnassignedContacts += 1;
            until Contact.Next() = 0;

        // Then assign contacts based on Excel data
        Counter := 0;
        if ContactUpdateBuffer.FindSet() then
            repeat
                Counter += 1;
                ProgressDialog.Update(1, StrSubstNo('%1 of %2', Counter, TotalRecords));

                CompanyName := ContactUpdateBuffer."Customer Name";

                // Find contacts with matching company name
                Contact.Reset();
                Contact.SetRange("Company Name", CompanyName);
                if Contact.FindSet(true) then begin
                    repeat
                        Contact."Salesperson Code" := SalespersonCode;
                        Contact.Modify(true);
                        UpdatedContacts += 1;
                    until Contact.Next() = 0;
                end else
                    NotFoundCompanies += 1;

            until ContactUpdateBuffer.Next() = 0;

        ProgressDialog.Close();

        // Build result text
        ResultText := StrSubstNo('Contact update completed successfully!\' +
                                '\' +
                                'Summary:\' +
                                '- Contacts assigned to %1: %2\' +
                                '- Contacts previously unassigned: %3\' +
                                '- Company names not found: %4\' +
                                '\' +
                                'Total customer names processed: %5',
                                SalespersonCode,
                                UpdatedContacts,
                                UnassignedContacts,
                                NotFoundCompanies,
                                TotalRecords);

        exit(ResultText);
    end;

    procedure ProcessOpportunityUpdates(SalespersonCode: Code[20]): Text
    var
        Contact: Record Contact;
        Opportunity: Record Opportunity;
        UpdatedOpportunities: Integer;
        ResultText: Text;
        ProgressDialog: Dialog;
        Counter: Integer;
        TotalContacts: Integer;
    begin
        UpdatedOpportunities := 0;

        // Count contacts assigned to this salesperson for progress
        Contact.SetRange("Salesperson Code", SalespersonCode);
        TotalContacts := Contact.Count();

        if TotalContacts = 0 then begin
            ResultText := StrSubstNo('No contacts found assigned to salesperson %1. No opportunities updated.', SalespersonCode);
            exit(ResultText);
        end;

        ProgressDialog.Open('Processing opportunity updates...\Progress: #1######');

        // Find all contacts assigned to this salesperson
        Counter := 0;
        if Contact.FindSet() then
            repeat
                Counter += 1;
                ProgressDialog.Update(1, StrSubstNo('%1 of %2', Counter, TotalContacts));

                // Find opportunities linked to this contact
                Opportunity.Reset();
                Opportunity.SetRange("Contact No.", Contact."No.");
                if Opportunity.FindSet(true) then begin
                    repeat
                        // Only update if the opportunity doesn't already have this salesperson assigned
                        if Opportunity."Salesperson Code" <> SalespersonCode then begin
                            Opportunity."Salesperson Code" := SalespersonCode;
                            Opportunity.Modify(true);
                            UpdatedOpportunities += 1;
                        end;
                    until Opportunity.Next() = 0;
                end;

            until Contact.Next() = 0;

        ProgressDialog.Close();

        // Build result text
        ResultText := StrSubstNo('Opportunity update completed!\' +
                                '\' +
                                'Opportunities updated: %1\' +
                                'Contacts processed: %2',
                                UpdatedOpportunities,
                                TotalContacts);

        exit(ResultText);
    end;
}
