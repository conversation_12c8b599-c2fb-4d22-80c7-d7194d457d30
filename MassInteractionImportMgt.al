codeunit 50131 "Mass Interaction Import Mgt"
{
    Access = Public;

    procedure ImportInteractions(var ExcelInStream: InStream; ImportMethod: Option "Contact Email","Contact No.")
    var
        ExcelBuffer: Record "Excel Buffer";
        InteractionLogEntry: Record "Interaction Log Entry";
        Contact: Record Contact;
        InteractionTemplate: Record "Interaction Template";
        CompanyContact: Record Contact;
        CompanyName: Text[100];
        CompanyNo: Code[20];
        LineNo: Integer;
        InteractionCode: Text;
        ContactEmail: Text;
        ContactNo: Code[20];
        ContactIdentifier: Text[100];
        InteractionScoreManagement: Codeunit "Interaction Score Management";
        Campaign: Record Campaign;
        CampaignNoExcel: Code[20];
        CampaignNoActual: Code[20];
        TempText: Text[100];
        Comment: Text;
        InteractionLogEntryCommentLine: Record "Inter. Log Entry Comment Line";
        ImportResult: Record "Mass Interaction Import Result" temporary;
        SuccessCount: Integer;
        SkipCount: Integer;
        ResultEntryNo: Integer;
    begin
        // Clear Excel Buffer
        ExcelBuffer.DeleteAll();

        // Import Excel file into Excel Buffer
        ExcelBuffer.OpenBookStream(ExcelInStream, 'Sheet1'); // Assuming data is in Sheet1
        ExcelBuffer.ReadSheet();

        // Validate header (optional, but good practice)
        // Assuming first row is header, skip it
        LineNo := 2; // Start reading from the second row

        // Read data from Excel Buffer
        ExcelBuffer.Reset();
        ExcelBuffer.SetRange("Row No.", LineNo, 999999); // Read from LineNo onwards
        if ExcelBuffer.FindSet() then begin
            repeat
                // Reset per-row variables
                InteractionCode := '';
                ContactEmail := '';
                ContactNo := '';
                ContactIdentifier := '';
                CampaignNoExcel := '';
                TempText := '';
                Comment := '';

                // Read Interaction Code from column 1
                ExcelBuffer.SetRange("Row No.", ExcelBuffer."Row No."); // Set current row for column filtering
                ExcelBuffer.SetRange("Column No.", 1);
                if ExcelBuffer.FindFirst() then
                    InteractionCode := ExcelBuffer."Cell Value as Text";
                ExcelBuffer.SetRange("Column No."); // Clear column filter

                // Read Contact Email or Contact No. from column 2 based on ImportMethod
                ExcelBuffer.SetRange("Row No.", ExcelBuffer."Row No."); // Set current row for column filtering
                ExcelBuffer.SetRange("Column No.", 2);
                if ExcelBuffer.FindFirst() then begin
                    if ImportMethod = ImportMethod::"Contact Email" then begin
                        ContactEmail := ExcelBuffer."Cell Value as Text";
                        ContactIdentifier := ContactEmail;
                    end else begin
                        ContactNo := CopyStr(ExcelBuffer."Cell Value as Text", 1, MaxStrLen(ContactNo));
                        ContactIdentifier := ContactNo;
                    end;
                end;
                ExcelBuffer.SetRange("Column No."); // Clear column filter

                // Read Campaign No. from column 3 (optional)
                ExcelBuffer.SetRange("Row No.", ExcelBuffer."Row No.");
                ExcelBuffer.SetRange("Column No.", 3);
                if ExcelBuffer.FindFirst() then begin
                    TempText := ExcelBuffer."Cell Value as Text";
                    // Truncate to fit Code[20] length
                    CampaignNoExcel := CopyStr(TempText, 1, MaxStrLen(CampaignNoExcel));
                end else
                    CampaignNoExcel := '';
                ExcelBuffer.SetRange("Column No.");

                // Read Comment from column 4 (optional)
                ExcelBuffer.SetRange("Row No.", ExcelBuffer."Row No.");
                ExcelBuffer.SetRange("Column No.", 4);
                if ExcelBuffer.FindFirst() then begin
                    Comment := ExcelBuffer."Cell Value as Text";
                end else
                    Comment := '';
                ExcelBuffer.SetRange("Column No.");

                // Process the data
                if (InteractionCode <> '') and (ContactIdentifier <> '') then begin
                    // Find Contact by Email or Contact No.
                    Clear(Contact);
                    if ImportMethod = ImportMethod::"Contact Email" then begin
                        Contact.SetRange("E-Mail", ContactEmail);
                    end else begin
                        Contact.SetRange("No.", ContactNo);
                    end;

                    if Contact.FindFirst() then begin
                        // Find Interaction Template
                        InteractionTemplate.SetRange(Code, InteractionCode);
                        if InteractionTemplate.FindFirst() then begin
                            // Resolve Campaign (optional)
                            CampaignNoActual := '';
                            if CampaignNoExcel <> '' then begin
                                Campaign.SetRange("No.", CampaignNoExcel);
                                if Campaign.FindFirst() then
                                    CampaignNoActual := Campaign."No.";
                            end;

                            // Create Interaction Log Entry
                            Clear(InteractionLogEntry); // start with a fresh record to avoid duplicate primary key
                            // Determine next available Entry No.
                            if InteractionLogEntry.FindLast() then
                                InteractionLogEntry."Entry No." := InteractionLogEntry."Entry No." + 1
                            else
                                InteractionLogEntry."Entry No." := 1;

                            InteractionLogEntry.Init();
                            InteractionLogEntry."Contact No." := Contact."No.";
                            InteractionLogEntry."Contact Name" := Contact.Name;
                            // Determine company information – works for both person and company contacts
                            CompanyNo := '';
                            CompanyName := '';

                            // For person contacts, "Company No." references the related company contact.
                            if Contact."Company No." <> '' then begin
                                CompanyNo := Contact."Company No.";
                                if CompanyContact.Get(CompanyNo) then
                                    CompanyName := CompanyContact.Name;
                            end;

                            // If we still do not have a company name (for company type contacts),
                            // fall back to the contact's own Company Name field or Name field.
                            if CompanyName = '' then
                                CompanyName := Contact."Company Name";

                            if CompanyName = '' then
                                CompanyName := Contact.Name;

                            InteractionLogEntry."Contact Company No." := CompanyNo;
                            InteractionLogEntry."Contact Company Name" := CompanyName;
                            InteractionLogEntry."Salesperson Code" := Contact."Salesperson Code";
                            InteractionLogEntry."Interaction Template Code" := InteractionTemplate.Code;
                            InteractionLogEntry."Date" := Today();
                            InteractionLogEntry."Time of Interaction" := Time();
                            InteractionLogEntry."Campaign No." := CampaignNoActual;
                            InteractionLogEntry."Campaign Entry No." := 0; // Optional: Add campaign entry if needed
                            InteractionLogEntry.Description := InteractionTemplate.Description;
                            InteractionLogEntry.Insert(true); // true to run triggers

                            // Add comment to Inter. Log Entry Comment Line if provided
                            if Comment <> '' then begin
                                Clear(InteractionLogEntryCommentLine);
                                InteractionLogEntryCommentLine."Entry No." := InteractionLogEntry."Entry No.";
                                InteractionLogEntryCommentLine."Line No." := 1;
                                InteractionLogEntryCommentLine.Date := Today();
                                // Handle comments longer than 80 characters by splitting into multiple lines if needed
                                if StrLen(Comment) > 80 then begin
                                    // Split long comments into multiple lines
                                    SplitLongComment(InteractionLogEntry."Entry No.", Comment);
                                end else begin
                                    InteractionLogEntryCommentLine.Comment := Comment;
                                    InteractionLogEntryCommentLine.Insert();
                                end;
                            end;

                            // Add success result entry
                            ImportResult.Init();
                            ResultEntryNo += 1;
                            ImportResult."Entry No." := ResultEntryNo;
                            ImportResult."Row No." := ExcelBuffer."Row No.";
                            ImportResult."Contact Email" := Contact."E-Mail";
                            ImportResult."Contact No." := Contact."No.";
                            ImportResult."Contact Identifier" := ContactIdentifier;
                            ImportResult."Interaction Code" := InteractionCode;
                            ImportResult."Campaign No." := CampaignNoActual;
                            ImportResult.Comment := Comment;
                            ImportResult.Status := ImportResult.Status::Imported;
                            ImportResult.Message := 'Imported successfully';
                            ImportResult.Insert();
                            SuccessCount += 1;
                        end else begin
                            ImportResult.Init();
                            ResultEntryNo += 1;
                            ImportResult."Entry No." := ResultEntryNo;
                            ImportResult."Row No." := ExcelBuffer."Row No.";
                            ImportResult."Contact Email" := ContactEmail;
                            ImportResult."Contact No." := ContactNo;
                            ImportResult."Contact Identifier" := ContactIdentifier;
                            ImportResult."Interaction Code" := InteractionCode;
                            ImportResult."Campaign No." := '';
                            ImportResult.Comment := Comment;
                            ImportResult.Status := ImportResult.Status::Skipped;
                            ImportResult.Message := StrSubstNo('Interaction Template Code "%1" not found', InteractionCode);
                            ImportResult.Insert();
                            SkipCount += 1;
                        end;
                    end else begin
                        ImportResult.Init();
                        ResultEntryNo += 1;
                        ImportResult."Entry No." := ResultEntryNo;
                        ImportResult."Row No." := ExcelBuffer."Row No.";
                        ImportResult."Contact Email" := ContactEmail;
                        ImportResult."Contact No." := ContactNo;
                        ImportResult."Contact Identifier" := ContactIdentifier;
                        ImportResult."Interaction Code" := InteractionCode;
                        ImportResult."Campaign No." := '';
                        ImportResult.Comment := Comment;
                        ImportResult.Status := ImportResult.Status::Skipped;
                        if ImportMethod = ImportMethod::"Contact Email" then
                            ImportResult.Message := 'Contact not found by email'
                        else
                            ImportResult.Message := 'Contact not found by contact number';
                        ImportResult.Insert();
                        SkipCount += 1;
                    end;
                end else begin
                    ImportResult.Init();
                    ResultEntryNo += 1;
                    ImportResult."Entry No." := ResultEntryNo;
                    ImportResult."Row No." := ExcelBuffer."Row No.";
                    ImportResult."Contact Email" := ContactEmail;
                    ImportResult."Contact No." := ContactNo;
                    ImportResult."Contact Identifier" := ContactIdentifier;
                    ImportResult."Interaction Code" := InteractionCode;
                    ImportResult."Campaign No." := '';
                    ImportResult.Comment := Comment;
                    ImportResult.Status := ImportResult.Status::Skipped;
                    if ImportMethod = ImportMethod::"Contact Email" then
                        ImportResult.Message := 'Missing Interaction Code or Contact Email'
                    else
                        ImportResult.Message := 'Missing Interaction Code or Contact No.';
                    ImportResult.Insert();
                    SkipCount += 1;
                end;

                LineNo += 1;
                ExcelBuffer.SetRange("Row No.", LineNo, 999999); // Move to next row
            until ExcelBuffer.Next() = 0;

            // Show summary page
            if not ImportResult.IsEmpty() then begin
                ImportResult.Reset();
                COMMIT; // Ensure database changes are committed before opening UI page
                Page.RunModal(Page::"Mass Interaction Import Result", ImportResult);
            end;
            Message('Import completed. Successfully imported: %1. Skipped: %2.', SuccessCount, SkipCount);
        end;
    end;

    local procedure SplitLongComment(EntryNo: Integer; LongComment: Text)
    var
        InteractionLogEntryCommentLine: Record "Inter. Log Entry Comment Line";
        CommentChunk: Text;
        LineNo: Integer;
        StartPos: Integer;
        ChunkSize: Integer;
    begin
        LineNo := 1;
        StartPos := 1;
        ChunkSize := 80; // Maximum characters per line

        while StartPos <= StrLen(LongComment) do begin
            Clear(InteractionLogEntryCommentLine);
            InteractionLogEntryCommentLine."Entry No." := EntryNo;
            InteractionLogEntryCommentLine."Line No." := LineNo;
            InteractionLogEntryCommentLine.Date := Today();

            // Extract chunk of the comment
            if StartPos + ChunkSize - 1 <= StrLen(LongComment) then
                CommentChunk := CopyStr(LongComment, StartPos, ChunkSize)
            else
                CommentChunk := CopyStr(LongComment, StartPos);

            InteractionLogEntryCommentLine.Comment := CommentChunk;
            InteractionLogEntryCommentLine.Insert();

            StartPos += ChunkSize;
            LineNo += 1;
        end;
    end;
}