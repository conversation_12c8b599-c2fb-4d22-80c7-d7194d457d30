// Table to log detailed mass email send attempts
table 50114 "Mass Email Log"
{
    Caption = 'Mass Email Log';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            DataClassification = CustomerContent;
            AutoIncrement = true;
        }
        field(2; "Job ID"; Code[20])
        {
            Caption = 'Job ID';
            DataClassification = CustomerContent;
            TableRelation = "Mass Email Queue"."Job ID";
        }
        field(3; "Recipient Line No."; Integer)
        {
            Caption = 'Recipient Line No.';
            DataClassification = CustomerContent;
        }
        field(4; "Log Date Time"; DateTime)
        {
            Caption = 'Log Date Time';
            DataClassification = CustomerContent;
        }
        field(5; "Log Type"; Enum "Mass Email Log Type")
        {
            Caption = 'Log Type';
            DataClassification = CustomerContent;
        }
        field(6; "Contact No."; Code[20])
        {
            Caption = 'Contact No.';
            DataClassification = CustomerContent;
            TableRelation = Contact;
        }
        field(7; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
            DataClassification = CustomerContent;
        }
        field(8; "E-Mail"; Text[80])
        {
            Caption = 'E-Mail';
            DataClassification = CustomerContent;
        }
        field(9; "Email Subject"; Text[250])
        {
            Caption = 'Email Subject';
            DataClassification = CustomerContent;
        }
        field(10; "Send Status"; Enum "Mass Email Send Status")
        {
            Caption = 'Send Status';
            DataClassification = CustomerContent;
        }
        field(11; "Error Message"; Text[2048])
        {
            Caption = 'Error Message';
            DataClassification = CustomerContent;
        }
        field(12; "Processing Time (ms)"; Integer)
        {
            Caption = 'Processing Time (ms)';
            DataClassification = CustomerContent;
        }
        field(13; "Batch No."; Integer)
        {
            Caption = 'Batch No.';
            DataClassification = CustomerContent;
        }
        field(14; "Retry Attempt"; Integer)
        {
            Caption = 'Retry Attempt';
            DataClassification = CustomerContent;
        }
        field(15; "Campaign No."; Code[20])
        {
            Caption = 'Campaign No.';
            DataClassification = CustomerContent;
            TableRelation = Campaign;
        }
        field(16; "Salesperson Code"; Code[20])
        {
            Caption = 'Salesperson Code';
            DataClassification = CustomerContent;
            TableRelation = "Salesperson/Purchaser";
        }
        field(17; "User ID"; Code[50])
        {
            Caption = 'User ID';
            DataClassification = CustomerContent;
        }
        field(18; "Additional Info"; Text[2048])
        {
            Caption = 'Additional Info';
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(Job; "Job ID", "Log Date Time")
        {
        }
        key(Contact; "Contact No.", "Log Date Time")
        {
        }
        key(Status; "Send Status", "Log Date Time")
        {
        }
        key(LogType; "Log Type", "Log Date Time")
        {
        }
    }

    trigger OnInsert()
    begin
        if "Log Date Time" = 0DT then
            "Log Date Time" := CurrentDateTime();
        
        if "User ID" = '' then
            "User ID" := CopyStr(UserId(), 1, MaxStrLen("User ID"));
    end;

    procedure LogEmailSent(JobID: Code[20]; RecipientLineNo: Integer; ContactNo: Code[20]; ContactName: Text[100]; EmailAddress: Text[80]; EmailSubject: Text[250]; ProcessingTimeMs: Integer; BatchNo: Integer; CampaignNo: Code[20]; SalespersonCode: Code[20])
    begin
        Init();
        "Job ID" := JobID;
        "Recipient Line No." := RecipientLineNo;
        "Log Type" := "Log Type"::"Email Sent";
        "Contact No." := ContactNo;
        "Contact Name" := CopyStr(ContactName, 1, MaxStrLen("Contact Name"));
        "E-Mail" := CopyStr(EmailAddress, 1, MaxStrLen("E-Mail"));
        "Email Subject" := CopyStr(EmailSubject, 1, MaxStrLen("Email Subject"));
        "Send Status" := "Send Status"::Sent;
        "Processing Time (ms)" := ProcessingTimeMs;
        "Batch No." := BatchNo;
        "Campaign No." := CampaignNo;
        "Salesperson Code" := SalespersonCode;
        Insert(true);
    end;

    procedure LogEmailFailed(JobID: Code[20]; RecipientLineNo: Integer; ContactNo: Code[20]; ContactName: Text[100]; EmailAddress: Text[80]; EmailSubject: Text[250]; ErrorMsg: Text; ProcessingTimeMs: Integer; BatchNo: Integer; RetryAttempt: Integer; CampaignNo: Code[20]; SalespersonCode: Code[20])
    begin
        Init();
        "Job ID" := JobID;
        "Recipient Line No." := RecipientLineNo;
        "Log Type" := "Log Type"::"Email Failed";
        "Contact No." := ContactNo;
        "Contact Name" := CopyStr(ContactName, 1, MaxStrLen("Contact Name"));
        "E-Mail" := CopyStr(EmailAddress, 1, MaxStrLen("E-Mail"));
        "Email Subject" := CopyStr(EmailSubject, 1, MaxStrLen("Email Subject"));
        "Send Status" := "Send Status"::Failed;
        "Error Message" := CopyStr(ErrorMsg, 1, MaxStrLen("Error Message"));
        "Processing Time (ms)" := ProcessingTimeMs;
        "Batch No." := BatchNo;
        "Retry Attempt" := RetryAttempt;
        "Campaign No." := CampaignNo;
        "Salesperson Code" := SalespersonCode;
        Insert(true);
    end;

    procedure LogJobEvent(JobID: Code[20]; LogType: Enum "Mass Email Log Type"; Message: Text; AdditionalInfo: Text)
    begin
        Init();
        "Job ID" := JobID;
        "Log Type" := LogType;
        "Error Message" := CopyStr(Message, 1, MaxStrLen("Error Message"));
        "Additional Info" := CopyStr(AdditionalInfo, 1, MaxStrLen("Additional Info"));
        Insert(true);
    end;
}
