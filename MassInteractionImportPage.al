page 50130 "Mass Interaction Import"
{
    Caption = 'Mass Interaction Import';
    ApplicationArea = All;
    UsageCategory = Tasks;
    SourceTable = "Excel Buffer"; // Using Excel Buffer as source table for file handling

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'Import Excel File';
                field(FileName; FileName)
                {
                    ApplicationArea = All;
                    Caption = 'File Name';
                    Editable = false;
                    ToolTip = 'Specifies the name of the Excel file to import.';
                }
                field(ImportMethod; ImportMethod)
                {
                    ApplicationArea = All;
                    Caption = 'Import Method';
                    ToolTip = 'Specifies whether to import by Contact Email or Contact No.';
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action(SelectFile)
            {
                Caption = 'Select File';
                ApplicationArea = All;
                Image = Import;
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Select the Excel file containing interaction data.';

                trigger OnAction()
                begin
                    UploadIntoStream('Select an Excel file to import', '', 'Excel Files|*.xlsx', FileName, InStream);
                    if FileName <> '' then begin
                        Message('File %1 selected successfully.', FileName);
                    end else begin
                        FileName := '';
                        Clear(InStream); // Clear InStream if no file selected
                        Message('No file selected.');
                    end;
                end;
            }
            action(Import)
            {
                Caption = 'Import Interactions';
                ApplicationArea = All;
                Image = ExecuteBatch;
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Import interaction data from the selected Excel file.';

                trigger OnAction()
                var
                    MassInteractionImportMgt: Codeunit "Mass Interaction Import Mgt";
                begin
                    if FileName = '' then // Check if a file has been selected
                        Error('Please select an Excel file first.');

                    MassInteractionImportMgt.ImportInteractions(InStream, ImportMethod);
                    Message('Import process completed. Check the Interaction Log Entries for details.');
                    FileName := '';
                    Clear(InStream); // Clear InStream after import
                end;
            }
        }
    }

    var
        FileName: Text;
        InStream: InStream;
        ImportMethod: Option "Contact Email","Contact No.";
}