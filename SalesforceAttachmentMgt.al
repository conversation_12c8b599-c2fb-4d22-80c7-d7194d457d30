codeunit 50103 "Salesforce Attachment Mgt."
{
    var
        TempSalesforceMapping: Record "Salesforce Doc Mapping" temporary;
        TempDocumentImport: Record "Temp Document Import" temporary;
        TempUpdatedOpportunities: Record "Updated Opportunity" temporary;
        NextUpdatedOppLineNo: Integer;
        TotalMappingsCount: Integer;
        TotalDocumentsCount: Integer;
        AttachmentsCreatedCount: Integer;
        AttachmentsSkippedCount: Integer;
        OpportunitiesNotFoundCount: Integer;

    procedure ImportCSVMapping()
    var
        UploadDialog: Dialog;
        ServerFileName: Text;
        InStream: InStream;
        FileName: Text;
        FileFilter: Text;
        LineNo: Integer;
        CSVLine: Text;
        CSVHeader: Text;
        CSVBuffer: Record "CSV Buffer" temporary;
        IdColumnNo: Integer;
        SalesforceOppIdColumnNo: Integer;
        PathOnClientColumnNo: Integer;
        ColumnValue: Text;
    begin
        // Initialize
        TempSalesforceMapping.Reset();
        TempSalesforceMapping.DeleteAll();
        TotalMappingsCount := 0;

        FileFilter := 'CSV Files (*.csv)|*.csv';
        UploadDialog.Open('Please select the CSV file with Salesforce document mappings...');
        if not UploadIntoStream('Select CSV File', '', FileFilter, FileName, InStream) then begin
            UploadDialog.Close();
            Error('Import canceled by user.');
        end;

        UploadDialog.Close();

        // Parse CSV file to find column indexes
        CSVBuffer.DeleteAll();
        CSVBuffer.LoadDataFromStream(InStream, ',');

        // Find column indices for Id, FirstPublishLocationId, and PathOnClient
        IdColumnNo := 0;
        SalesforceOppIdColumnNo := 0;
        PathOnClientColumnNo := 0;

        CSVBuffer.SetRange(CSVBuffer."Line No.", 1);  // Header line
        if CSVBuffer.FindSet() then begin
            repeat
                ColumnValue := CSVBuffer.Value;
                // Remove quotation marks if present
                ColumnValue := DelChr(ColumnValue, '<>', '"');
                // Trim whitespace
                ColumnValue := DelChr(ColumnValue, '<>', ' ');

                if ColumnValue = 'Id' then
                    IdColumnNo := CSVBuffer."Field No."
                else if ColumnValue = 'FirstPublishLocationId' then
                    SalesforceOppIdColumnNo := CSVBuffer."Field No."
                else if ColumnValue = 'PathOnClient' then
                    PathOnClientColumnNo := CSVBuffer."Field No.";
            until CSVBuffer.Next() = 0;
        end;

        if (IdColumnNo = 0) or (SalesforceOppIdColumnNo = 0) then begin
            if IdColumnNo = 0 then
                Error('Column "Id" not found in CSV file.');
            if SalesforceOppIdColumnNo = 0 then
                Error('Column "FirstPublishLocationId" not found in CSV file.');
        end;

        // Read mapping data
        CSVBuffer.Reset();
        CSVBuffer.SetFilter(CSVBuffer."Line No.", '>1');  // Skip header

        if CSVBuffer.FindSet() then begin
            LineNo := 0;

            repeat
                if CSVBuffer."Field No." = IdColumnNo then begin
                    LineNo := CSVBuffer."Line No.";

                    TempSalesforceMapping.Init();
                    TempSalesforceMapping."Entry No." := LineNo;
                    TempSalesforceMapping."Document Id" := DelChr(CSVBuffer.Value, '<>', '"');  // Remove quotes if present

                    // Find corresponding SalesforceOppId in the same row
                    CSVBuffer.SetRange("Line No.", LineNo);
                    CSVBuffer.SetRange("Field No.", SalesforceOppIdColumnNo);

                    if CSVBuffer.FindFirst() then
                        TempSalesforceMapping."Salesforce Opp Id" := DelChr(CSVBuffer.Value, '<>', '"');  // Remove quotes if present

                    // Find corresponding PathOnClient in the same row
                    if PathOnClientColumnNo > 0 then begin
                        CSVBuffer.SetRange("Line No.", LineNo);
                        CSVBuffer.SetRange("Field No.", PathOnClientColumnNo);

                        if CSVBuffer.FindFirst() then
                            TempSalesforceMapping."PathOnClient" := DelChr(CSVBuffer.Value, '<>', '"');  // Remove quotes if present
                    end;

                    if (TempSalesforceMapping."Document Id" <> '') and
                       (TempSalesforceMapping."Salesforce Opp Id" <> '') then begin
                        TempSalesforceMapping.Insert();
                        TotalMappingsCount += 1;
                    end;

                    // Reset filters for next iteration
                    CSVBuffer.Reset();
                    CSVBuffer.SetFilter(CSVBuffer."Line No.", '>1');
                    CSVBuffer.SetRange("Field No.");
                    CSVBuffer.SetFilter("Line No.", '>%1', LineNo);
                    CSVBuffer.SetRange("Field No.", IdColumnNo);
                end;
            until CSVBuffer.Next() = 0;
        end;

        if TotalMappingsCount = 0 then
            Error('No valid mapping records found in the CSV file.');
    end;

    procedure ImportDocuments()
    var
        DocumentAttachment: Record "Document Attachment";
        AttachmentCount: Integer;
        ReadyMsg: Label 'Ready to process %1 document attachments.';
        NoAttachmentsMsg: Label 'No document attachments found. Please attach documents using the Attachments section before proceeding.';
    begin
        // Clean up any existing temporary document records
        TempDocumentImport.Reset();
        TempDocumentImport.DeleteAll();
        TotalDocumentsCount := 0;

        // Check how many document attachments exist
        DocumentAttachment.Reset();
        DocumentAttachment.SetRange("Table ID", 50137);  // Temp Document Import table ID
        DocumentAttachment.SetRange("No.", '0');

        // Count actual documents (excluding empty records)
        if DocumentAttachment.FindSet() then begin
            repeat
                if (DocumentAttachment."File Name" <> '') and DocumentAttachment.HasContent() then
                    TotalDocumentsCount += 1;
            until DocumentAttachment.Next() = 0;
        end;

        // If there are no document attachments, exit with a message
        if TotalDocumentsCount = 0 then begin
            Message(NoAttachmentsMsg);
            exit;
        end;

        // Process each document attachment with content
        DocumentAttachment.Reset();
        DocumentAttachment.SetRange("Table ID", 50137);
        DocumentAttachment.SetRange("No.", '0');

        if DocumentAttachment.FindSet() then begin
            repeat
                if (DocumentAttachment."File Name" <> '') and DocumentAttachment.HasContent() then begin
                    TempDocumentImport.Init();
                    TempDocumentImport."Entry No." := TempDocumentImport."Entry No." + 1;
                    TempDocumentImport."Document Name" := RemoveFileExtension(DocumentAttachment."File Name");
                    TempDocumentImport.Insert();
                end;
            until DocumentAttachment.Next() = 0;
        end;

        Message(ReadyMsg, TotalDocumentsCount);
    end;

    procedure ProcessAttachments()
    var
        Opportunity: Record Opportunity;
        DocAttachment: Record "Document Attachment";
        SourceDocAttachment: Record "Document Attachment";
        RecRef: RecordRef;
        ProgressDialog: Dialog;
        ProgressMsg: Label 'Processing document #1##### of #2#####...';
        Counter: Integer;
        TotalCount: Integer;
        DocumentId: Text;
        UploadedFileName: Text;
        BaseFileName: Text;
        FileExt: Text;
    begin
        // Initialize counters
        AttachmentsCreatedCount := 0;
        AttachmentsSkippedCount := 0;
        OpportunitiesNotFoundCount := 0;

        // Gather the list of uploaded source documents (table 50137, No. = '0')
        SourceDocAttachment.Reset();
        SourceDocAttachment.SetRange("Table ID", 50137);
        SourceDocAttachment.SetRange("No.", '0');

        // Determine total count for progress dialog
        TotalCount := SourceDocAttachment.Count;
        if TotalCount = 0 then
            Error('No documents to process. Please import documents first.');

        ProgressDialog.Open(ProgressMsg, Counter, TotalCount);

        if SourceDocAttachment.FindSet() then
            repeat
                Counter += 1;
                ProgressDialog.Update(1, Counter);

                // The Id column is exactly the file name of the uploaded document (no extension)
                DocumentId := SourceDocAttachment."File Name";

                // Locate mapping rows that refer to this document Id
                TempSalesforceMapping.Reset();
                TempSalesforceMapping.SetRange("Document Id", DocumentId);

                if TempSalesforceMapping.FindSet() then
                    repeat
                        // Locate the target opportunity using FirstPublishLocationId (SalesforceOppId)
                        Opportunity.Reset();
                        Opportunity.SetRange("SalesforceOppId", TempSalesforceMapping."Salesforce Opp Id");

                        if Opportunity.FindFirst() then begin
                            // Decide what file name the attachment should have in BC
                            if TempSalesforceMapping."PathOnClient" <> '' then
                                UploadedFileName := TempSalesforceMapping."PathOnClient"
                            else
                                UploadedFileName := SourceDocAttachment."File Name"; // fallback – keep original

                            // Extract the actual file name and strip the extension so Business Central can add it back automatically
                            UploadedFileName := ExtractFileName(UploadedFileName);
                            BaseFileName := RemoveFileExtension(UploadedFileName);
                            FileExt := GetFileExtension(UploadedFileName);
                            if BaseFileName = '' then
                                BaseFileName := UploadedFileName; // Handles files without extensions
                            if FileExt = '' then
                                FileExt := GetFileExtension(SourceDocAttachment."File Name");

                            // If an attachment with the same file name already exists, update its content
                            DocAttachment.Reset();
                            DocAttachment.SetRange("Table ID", DATABASE::Opportunity);
                            DocAttachment.SetRange("No.", Opportunity."No.");
                            DocAttachment.SetRange("File Name", BaseFileName);

                            if not DocAttachment.FindFirst() then begin
                                RecRef.GetTable(Opportunity);
                                CopyAttachment(SourceDocAttachment, RecRef, BaseFileName, FileExt);
                                AttachmentsCreatedCount += 1;
                                AddUpdatedOpportunity(Opportunity);
                            end else begin
                                // Update existing attachment's content to match the latest upload
                                DocAttachment."Document Reference ID" := SourceDocAttachment."Document Reference ID";
                                DocAttachment."File Type" := SourceDocAttachment."File Type";
                                if SourceDocAttachment."File Extension" <> '' then
                                    DocAttachment."File Extension" := SourceDocAttachment."File Extension"
                                else
                                    DocAttachment."File Extension" := FileExt;
                                DocAttachment.Modify();

                                AttachmentsSkippedCount += 1; // Count as skipped/updated
                                AddUpdatedOpportunity(Opportunity);
                            end;
                        end else
                            OpportunitiesNotFoundCount += 1;
                    until TempSalesforceMapping.Next() = 0;
            until SourceDocAttachment.Next() = 0;

        ProgressDialog.Close();
    end;

    local procedure CopyAttachment(var SourceAttachment: Record "Document Attachment"; var DestinationRecRef: RecordRef; NewFileName: Text; NewFileExtension: Text)
    var
        OpportunityNo: Code[20];
        DestFieldRef: FieldRef;
    begin
        // Get the Opportunity No. from the RecordRef
        DestFieldRef := DestinationRecRef.Field(1); // Field 1 is "No." in Opportunity table
        OpportunityNo := DestFieldRef.Value;

        // Simply insert the new attachment record directly
        InsertOpportunityAttachment(DATABASE::Opportunity, OpportunityNo, SourceAttachment, NewFileName, NewFileExtension);
    end;

    local procedure InsertOpportunityAttachment(TableID: Integer; RecordNo: Code[20]; SourceAttachment: Record "Document Attachment"; NewFileName: Text; NewFileExtension: Text)
    var
        NewDocAttachment: Record "Document Attachment";
    begin
        NewDocAttachment.Init();
        NewDocAttachment."Table ID" := TableID;
        NewDocAttachment."No." := RecordNo;
        NewDocAttachment."Attached Date" := CreateDateTime(Today, Time);
        NewDocAttachment."File Name" := NewFileName;
        NewDocAttachment."File Type" := SourceAttachment."File Type";

        // Determine the correct file extension
        if SourceAttachment."File Extension" <> '' then
            NewDocAttachment."File Extension" := SourceAttachment."File Extension"
        else
            NewDocAttachment."File Extension" := NewFileExtension;

        // Copy document reference ID
        NewDocAttachment."Document Reference ID" := SourceAttachment."Document Reference ID";

        if NewDocAttachment.Insert() then;
    end;

    procedure ClearImportedData()
    begin
        TempSalesforceMapping.Reset();
        TempSalesforceMapping.DeleteAll();

        TempDocumentImport.Reset();
        TempDocumentImport.DeleteAll();

        TotalMappingsCount := 0;
        TotalDocumentsCount := 0;
        AttachmentsCreatedCount := 0;
        AttachmentsSkippedCount := 0;
        OpportunitiesNotFoundCount := 0;

        TempUpdatedOpportunities.Reset();
        TempUpdatedOpportunities.DeleteAll();
        NextUpdatedOppLineNo := 0;
    end;

    procedure GetTotalMappingsCount(): Integer
    begin
        exit(TotalMappingsCount);
    end;

    procedure GetTotalDocumentsCount(): Integer
    begin
        exit(TotalDocumentsCount);
    end;

    procedure GetAttachmentsCreatedCount(): Integer
    begin
        exit(AttachmentsCreatedCount);
    end;

    procedure GetAttachmentsSkippedCount(): Integer
    begin
        exit(AttachmentsSkippedCount);
    end;

    procedure GetOpportunitiesNotFoundCount(): Integer
    begin
        exit(OpportunitiesNotFoundCount);
    end;

    local procedure RemoveFileExtension(FileName: Text): Text
    var
        DotPos: Integer;
    begin
        DotPos := StrLen(FileName);
        while DotPos > 0 do begin
            if CopyStr(FileName, DotPos, 1) = '.' then
                exit(CopyStr(FileName, 1, DotPos - 1));
            DotPos -= 1;
        end;

        exit(FileName);
    end;

    local procedure GetFileExtension(FileName: Text): Text
    var
        DotPos: Integer;
    begin
        DotPos := StrLen(FileName);
        while DotPos > 0 do begin
            if CopyStr(FileName, DotPos, 1) = '.' then
                exit(CopyStr(FileName, DotPos + 1));
            DotPos -= 1;
        end;

        exit('');
    end;

    local procedure AddUpdatedOpportunity(var OpportunityRec: Record Opportunity)
    begin
        // Avoid duplicates
        TempUpdatedOpportunities.SetRange("Opportunity No.", OpportunityRec."No.");
        if TempUpdatedOpportunities.IsEmpty() then begin
            TempUpdatedOpportunities.Reset();
            TempUpdatedOpportunities.Init();
            NextUpdatedOppLineNo += 1;
            TempUpdatedOpportunities."Line No." := NextUpdatedOppLineNo;
            TempUpdatedOpportunities."Opportunity No." := OpportunityRec."No.";
            TempUpdatedOpportunities."Opportunity Name" := OpportunityRec.Description; // Assuming Description field exists
            TempUpdatedOpportunities.Insert();
        end;
    end;

    procedure GetUpdatedOpportunities(var DestUpdatedOpportunities: Record "Updated Opportunity")
    begin
        DestUpdatedOpportunities.Reset();
        DestUpdatedOpportunities.DeleteAll();

        TempUpdatedOpportunities.Reset();
        if TempUpdatedOpportunities.FindSet() then
            repeat
                DestUpdatedOpportunities := TempUpdatedOpportunities;
                DestUpdatedOpportunities.Insert();
            until TempUpdatedOpportunities.Next() = 0;
    end;

    local procedure ExtractFileName(FilePath: Text): Text
    var
        i: Integer;
        Ch: Text[1];
    begin
        // Returns the last component of a path (handles both '/' and '\\' separators)
        for i := StrLen(FilePath) downto 1 do begin
            Ch := CopyStr(FilePath, i, 1);
            if (Ch = '/') or (Ch = '\\') then
                exit(CopyStr(FilePath, i + 1));
        end;
        exit(FilePath);
    end;
}