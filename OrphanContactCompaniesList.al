page 50148 "Orphan Contact Companies"
{
    PageType = List;
    SourceTable = Contact;
    SourceTableTemporary = true;
    ApplicationArea = All;
    UsageCategory = Lists;
    Caption = 'Contact Companies w/o Persons';
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;

                    trigger OnDrillDown()
                    var
                        RealContact: Record Contact;
                    begin
                        if RealContact.Get(Rec."No.") then
                            PAGE.Run(PAGE::"Contact Card", RealContact);
                    end;
                }
                field(Name; Rec.Name) { ApplicationArea = All; }
                field(Address; Rec.Address) { ApplicationArea = All; }
                field(City; Rec.City) { ApplicationArea = All; }
                field("Phone No."; Rec."Phone No.") { ApplicationArea = All; }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ExtendedDatatype = EMail;
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson Code';
                }
                field(Type; Rec.Type)
                {
                    ApplicationArea = All;
                    Caption = 'Contact Type';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(DeleteSelected)
            {
                Caption = 'Delete Selected';
                Image = Delete;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                var
                    Selected: Record Contact;
                    RealContact: Record Contact;
                    Cnt: Integer;
                begin
                    // Get the records user selected on the page
                    CurrPage.SetSelectionFilter(Selected);

                    if Selected.IsEmpty() then begin
                        Message('Please select at least one company contact to delete.');
                        exit;
                    end;

                    // Count selected records for confirmation
                    if Selected.FindSet() then
                        repeat
                            Cnt += 1;
                        until Selected.Next() = 0;

                    if not Confirm(StrSubstNo('Are you sure you want to delete %1 contact companie(s)? This action cannot be undone.', Cnt), false) then
                        exit;

                    // Delete each selected company contact in the real table
                    if Selected.FindSet() then
                        repeat
                            if RealContact.Get(Selected."No.") then
                                RealContact.Delete(true);
                        until Selected.Next() = 0;

                    // Refresh the data set
                    PopulateTemp();
                    CurrPage.Update(false);
                end;
            }
            action(RefreshData)
            {
                Caption = 'Refresh';
                Image = Refresh;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                begin
                    PopulateTemp();
                    CurrPage.Update(false);
                end;
            }
            action(ChangeSalesperson)
            {
                Caption = 'Change Salesperson';
                Image = SalesPerson;
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                var
                    Selected: Record Contact;
                    RealContact: Record Contact;
                    Salesperson: Record "Salesperson/Purchaser";
                    NewSPCode: Code[20];
                    SelectedCnt: Integer;
                begin
                    // Get selected temp records
                    CurrPage.SetSelectionFilter(Selected);

                    if Selected.IsEmpty() then begin
                        Message('Please select at least one company contact to update.');
                        exit;
                    end;

                    // Ask user to choose salesperson (0 = table's default lookup page)
                    if PAGE.RunModal(0, Salesperson) <> ACTION::LookupOK then
                        exit; // user cancelled

                    NewSPCode := Salesperson.Code;

                    // Count selected for confirmation
                    if Selected.FindSet() then
                        repeat
                            SelectedCnt += 1;
                        until Selected.Next() = 0;

                    if not Confirm(StrSubstNo('Assign salesperson %1 to %2 contact companie(s)?', Salesperson.Code, SelectedCnt), false) then
                        exit;

                    // Apply change on real contacts
                    if Selected.FindSet() then
                        repeat
                            if RealContact.Get(Selected."No.") then begin
                                RealContact."Salesperson Code" := NewSPCode;
                                RealContact.Modify(true);
                            end;
                        until Selected.Next() = 0;

                    // Refresh temp data
                    PopulateTemp();
                    CurrPage.Update(false);

                    Message('%1 contact companie(s) updated.', SelectedCnt);
                end;
            }
        }
    }

    trigger OnOpenPage()
    begin
        PopulateTemp();
    end;

    local procedure PopulateTemp()
    var
        Company: Record Contact;
        Person: Record Contact;
    begin
        // Clear existing temporary data
        Rec.Reset();
        Rec.DeleteAll();

        // Loop through all contact companies
        Company.SetRange(Type, Company.Type::Company);
        if Company.FindSet() then
            repeat
                // Check if company has any person contacts linked
                Person.Reset();
                Person.SetRange(Type, Person.Type::Person);
                Person.SetRange("Company No.", Company."No.");
                if not Person.FindFirst() then begin
                    // No linked persons -> add to the temp page record
                    Rec := Company;
                    Rec.Insert(false);
                end;
            until Company.Next() = 0;
    end;
}