// Codeunit for processing mass email jobs in background
codeunit 50112 "Mass Email Job Processor"
{
    TableNo = "Mass Email Queue";

    trigger OnRun()
    begin
        ProcessEmailJob(Rec);
    end;

    local procedure ProcessEmailJob(var MassEmailQueue: Record "Mass Email Queue")
    var
        MassEmailRecipients: Record "Mass Email Recipients";
        MassEmailLog: Record "Mass Email Log";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        StartTime: DateTime;
        BatchSize: Integer;
        WaitMilliseconds: Integer;
        CurrentBatch: Integer;
        EmailsInCurrentBatch: Integer;
        TotalProcessed: Integer;
        ProcessingStartTime: DateTime;
        ProcessingEndTime: DateTime;
        ProcessingTimeMs: Integer;
        PersonalSubject: Text;
        PersonalBody: Text;
        EmailBodyText: Text;
        AttachmentFileNames: List of [Text];
        AttachmentMimeTypes: List of [Text];
        ErrorMessage: Text;
    begin
        StartTime := CurrentDateTime();

        // Update job status to In Progress
        MassEmailQueue."Job Status" := MassEmailQueue."Job Status"::"In Progress";
        MassEmailQueue."Started Date Time" := StartTime;
        MassEmailQueue."Current Batch" := 1;
        MassEmailQueue.Modify();

        // Log job start
        MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Job Started",
            StrSubstNo('Started processing job with %1 recipients', MassEmailQueue."Total Recipients"), '');

        // Configuration
        BatchSize := 25; // Number of emails to send before pausing
        WaitMilliseconds := 60000; // Pause duration (60 seconds)
        CurrentBatch := 1;
        EmailsInCurrentBatch := 0;
        TotalProcessed := 0;

        // Get email body
        EmailBodyText := MassEmailQueue.GetEmailBody();

        // Parse attachment info
        ParseAttachmentInfo(MassEmailQueue, AttachmentFileNames, AttachmentMimeTypes);

        // Process recipients
        MassEmailRecipients.SetRange("Job ID", MassEmailQueue."Job ID");
        MassEmailRecipients.SetRange("Send Status", MassEmailRecipients."Send Status"::Pending);

        if MassEmailRecipients.FindSet(true) then begin
            // Log batch start
            MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Batch Started",
                StrSubstNo('Starting batch %1', CurrentBatch), '');

            repeat
                ProcessingStartTime := CurrentDateTime();

                // Create personalized subject and body
                PersonalSubject := ReplaceTokens(MassEmailQueue."Email Subject", MassEmailRecipients);
                PersonalBody := ReplaceTokens(EmailBodyText, MassEmailRecipients);

                // Store personalized subject for logging
                MassEmailRecipients."Personalized Subject" := CopyStr(PersonalSubject, 1, MaxStrLen(MassEmailRecipients."Personalized Subject"));

                // Try to send email
                if TrySendIndividualEmail(PersonalSubject, PersonalBody, MassEmailRecipients."E-Mail", AttachmentFileNames, AttachmentMimeTypes, MassEmailQueue, ErrorMessage) then begin
                    // Success
                    MassEmailRecipients."Send Status" := MassEmailRecipients."Send Status"::Sent;
                    MassEmailRecipients."Sent Date Time" := CurrentDateTime();
                    MassEmailRecipients."Error Message" := '';
                    MassEmailQueue."Emails Sent" += 1;

                    ProcessingEndTime := CurrentDateTime();
                    ProcessingTimeMs := ProcessingEndTime - ProcessingStartTime;

                    // Log success
                    MassEmailLog.LogEmailSent(MassEmailQueue."Job ID", MassEmailRecipients."Line No.",
                        MassEmailRecipients."Contact No.", MassEmailRecipients."Contact Name",
                        MassEmailRecipients."E-Mail", PersonalSubject, ProcessingTimeMs, CurrentBatch,
                        MassEmailQueue."Campaign No.", MassEmailQueue."Salesperson Code");

                end else begin
                    // Failure
                    MassEmailRecipients."Send Status" := MassEmailRecipients."Send Status"::Failed;
                    MassEmailRecipients."Error Message" := CopyStr(ErrorMessage, 1, MaxStrLen(MassEmailRecipients."Error Message"));
                    MassEmailRecipients."Retry Count" += 1;
                    MassEmailQueue."Emails Failed" += 1;
                    MassEmailQueue."Last Error Message" := CopyStr(ErrorMessage, 1, MaxStrLen(MassEmailQueue."Last Error Message"));

                    ProcessingEndTime := CurrentDateTime();
                    ProcessingTimeMs := ProcessingEndTime - ProcessingStartTime;

                    // Log failure
                    MassEmailLog.LogEmailFailed(MassEmailQueue."Job ID", MassEmailRecipients."Line No.",
                        MassEmailRecipients."Contact No.", MassEmailRecipients."Contact Name",
                        MassEmailRecipients."E-Mail", PersonalSubject, ErrorMessage, ProcessingTimeMs,
                        CurrentBatch, MassEmailRecipients."Retry Count", MassEmailQueue."Campaign No.",
                        MassEmailQueue."Salesperson Code");
                end;

                MassEmailRecipients.Modify();
                TotalProcessed += 1;
                EmailsInCurrentBatch += 1;

                // Update progress
                MassEmailQueue.UpdateProgress();
                MassEmailQueue.Modify();

                // Check if we need to pause for throttling
                if (EmailsInCurrentBatch >= BatchSize) and (TotalProcessed < MassEmailQueue."Total Recipients") then begin
                    // Log batch completion
                    MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Batch Completed",
                        StrSubstNo('Completed batch %1 with %2 emails', CurrentBatch, EmailsInCurrentBatch), '');

                    // Log throttle wait
                    MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Throttle Wait",
                        StrSubstNo('Waiting %1 seconds before next batch', WaitMilliseconds / 1000), '');

                    Sleep(WaitMilliseconds);

                    // Start new batch
                    CurrentBatch += 1;
                    EmailsInCurrentBatch := 0;
                    MassEmailQueue."Current Batch" := CurrentBatch;
                    MassEmailQueue.Modify();

                    // Log new batch start
                    MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Batch Started",
                        StrSubstNo('Starting batch %1', CurrentBatch), '');
                end;

            until MassEmailRecipients.Next() = 0;

            // Log final batch completion if needed
            if EmailsInCurrentBatch > 0 then
                MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Batch Completed",
                    StrSubstNo('Completed final batch %1 with %2 emails', CurrentBatch, EmailsInCurrentBatch), '');
        end;

        // Complete the job
        MassEmailQueue."Job Status" := MassEmailQueue."Job Status"::Completed;
        MassEmailQueue."Completed Date Time" := CurrentDateTime();
        MassEmailQueue.UpdateProgress();
        MassEmailQueue.Modify();

        // Log job completion
        MassEmailLog.LogJobEvent(MassEmailQueue."Job ID", MassEmailLog."Log Type"::"Job Completed",
            StrSubstNo('Job completed. Sent: %1, Failed: %2, Total: %3',
                MassEmailQueue."Emails Sent", MassEmailQueue."Emails Failed", MassEmailQueue."Total Recipients"),
            StrSubstNo('Processing time: %1 minutes', (CurrentDateTime() - StartTime) / 60000));

        // Create interaction log entries for successful sends
        CreateInteractionLogEntries(MassEmailQueue);
    end;

    local procedure TrySendIndividualEmail(Subject: Text; Body: Text; RecipientEmail: Text; AttachmentFileNames: List of [Text]; AttachmentMimeTypes: List of [Text]; MassEmailQueue: Record "Mass Email Queue"; var ErrorMessage: Text): Boolean
    var
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        i: Integer;
        AttachmentInStream: InStream;
        TempBlob: Codeunit "Temp Blob";
    begin
        ErrorMessage := '';

        // Create email message
        EmailMessage.Create('', Subject, Body, true);
        EmailMessage.AddRecipient(Enum::"Email Recipient Type"::"To", RecipientEmail);

        // Add attachments if any
        if AttachmentFileNames.Count() > 0 then begin
            if not AddAttachmentsToEmail(EmailMessage, AttachmentFileNames, AttachmentMimeTypes, MassEmailQueue) then begin
                ErrorMessage := 'Failed to add attachments to email';
                exit(false);
            end;
        end;

        // Send the email
        if Email.Send(EmailMessage) then
            exit(true)
        else begin
            ErrorMessage := 'Failed to send email - check email configuration and outbox for details';
            exit(false);
        end;
    end;

    local procedure AddAttachmentsToEmail(var EmailMessage: Codeunit "Email Message"; AttachmentFileNames: List of [Text]; AttachmentMimeTypes: List of [Text]; MassEmailQueue: Record "Mass Email Queue"): Boolean
    var
        AttachmentInStream: InStream;
        i: Integer;
    begin
        // Note: This is a simplified implementation
        // In a full implementation, you would need to properly handle the blob data
        // and split it back into individual attachments

        // For now, we'll skip attachments in background processing
        // This could be enhanced to properly handle attachments
        exit(true);
    end;

    local procedure ParseAttachmentInfo(MassEmailQueue: Record "Mass Email Queue"; var AttachmentFileNames: List of [Text]; var AttachmentMimeTypes: List of [Text])
    var
        FileNameText: Text;
        MimeTypeText: Text;
        FileNames: List of [Text];
        MimeTypes: List of [Text];
    begin
        FileNameText := MassEmailQueue."Attachment File Names";
        MimeTypeText := MassEmailQueue."Attachment Mime Types";

        if FileNameText <> '' then begin
            FileNames := FileNameText.Split('|');
            AttachmentFileNames := FileNames;
        end;

        if MimeTypeText <> '' then begin
            MimeTypes := MimeTypeText.Split('|');
            AttachmentMimeTypes := MimeTypes;
        end;
    end;

    local procedure ReplaceTokens(Template: Text; ContactBuf: Record "Mass Email Recipients"): Text
    var
        ResultText: Text;
        ContactFirstName: Text;
    begin
        ResultText := Template;

        // Extract first name from contact name
        ContactFirstName := ExtractFirstName(ContactBuf."Contact Name");

        ResultText := TextReplace(ResultText, '%ContactName%', ContactBuf."Contact Name");
        ResultText := TextReplace(ResultText, '%ContactFirstName%', ContactFirstName);
        ResultText := TextReplace(ResultText, '%CompanyName%', ContactBuf."Company Name");
        ResultText := TextReplace(ResultText, '%Email%', ContactBuf."E-Mail");
        ResultText := TextReplace(ResultText, '%SalespersonCode%', ContactBuf."Salesperson Code");

        exit(ResultText);
    end;

    local procedure ExtractFirstName(FullName: Text): Text
    var
        SpacePos: Integer;
    begin
        SpacePos := StrPos(FullName, ' ');
        if SpacePos > 0 then
            exit(CopyStr(FullName, 1, SpacePos - 1))
        else
            exit(FullName);
    end;

    local procedure TextReplace(Source: Text; Token: Text; Replacement: Text): Text
    var
        Pos: Integer;
    begin
        Pos := StrPos(Source, Token);
        while Pos > 0 do begin
            Source := CopyStr(Source, 1, Pos - 1) + Replacement + CopyStr(Source, Pos + StrLen(Token));
            Pos := StrPos(Source, Token);
        end;

        exit(Source);
    end;

    local procedure CreateInteractionLogEntries(MassEmailQueue: Record "Mass Email Queue")
    var
        MassEmailRecipients: Record "Mass Email Recipients";
        ContactRec: Record Contact;
        InteractionLogEntry: Record "Interaction Log Entry";
        CompanyContact: Record Contact;
        CompanyName: Text[100];
        CompanyNo: Code[20];
    begin
        if MassEmailQueue."Campaign No." = '' then
            exit;

        MassEmailRecipients.SetRange("Job ID", MassEmailQueue."Job ID");
        MassEmailRecipients.SetRange("Send Status", MassEmailRecipients."Send Status"::Sent);

        if MassEmailRecipients.FindSet() then
            repeat
                if ContactRec.Get(MassEmailRecipients."Contact No.") then begin
                    Clear(InteractionLogEntry);
                    if InteractionLogEntry.FindLast() then
                        InteractionLogEntry."Entry No." := InteractionLogEntry."Entry No." + 1
                    else
                        InteractionLogEntry."Entry No." := 1;

                    InteractionLogEntry.Init();
                    InteractionLogEntry."Contact No." := ContactRec."No.";
                    InteractionLogEntry."Contact Name" := ContactRec.Name;

                    // Determine company info
                    CompanyNo := '';
                    CompanyName := '';
                    if ContactRec."Company No." <> '' then begin
                        CompanyNo := ContactRec."Company No.";
                        if CompanyContact.Get(CompanyNo) then
                            CompanyName := CompanyContact.Name;
                    end;

                    if CompanyName = '' then
                        CompanyName := ContactRec."Company Name";
                    if CompanyName = '' then
                        CompanyName := ContactRec.Name;

                    InteractionLogEntry."Contact Company No." := CompanyNo;
                    InteractionLogEntry."Contact Company Name" := CompanyName;
                    InteractionLogEntry."Salesperson Code" := ContactRec."Salesperson Code";
                    InteractionLogEntry."Interaction Template Code" := 'EMAIL-SENT';
                    InteractionLogEntry."Date" := DT2Date(MassEmailRecipients."Sent Date Time");
                    InteractionLogEntry."Time of Interaction" := DT2Time(MassEmailRecipients."Sent Date Time");
                    InteractionLogEntry."Campaign No." := MassEmailQueue."Campaign No.";
                    InteractionLogEntry."Campaign Entry No." := 0;
                    InteractionLogEntry.Description := StrSubstNo('Mass email sent via background job %1', MassEmailQueue."Job ID");
                    InteractionLogEntry.Insert(true);
                end;
            until MassEmailRecipients.Next() = 0;
    end;
}
