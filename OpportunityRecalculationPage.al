namespace DefaultPublisher.ALProject1;

using Microsoft.CRM.Opportunity;

codeunit 50104 "Opportunity Recalculation Mgt."
{
    trigger OnRun()
    begin
        RecalculateAllOpportunities();
    end;

    procedure RecalculateAllOpportunities()
    var
        Opportunity: Record Opportunity;
        ConfirmQst: Label 'This will recalculate financial fields for all opportunities. This process may take some time. Do you want to continue?';
        CompleteMsg: Label 'Recalculation complete. %1 opportunities processed, %2 opportunities updated.';
        ProcessedCount: Integer;
        UpdatedCount: Integer;
        TotalCount: Integer;
    begin
        if not Confirm(ConfirmQst) then
            exit;

        Opportunity.Reset();
        TotalCount := Opportunity.Count();
        ProcessedCount := 0;
        UpdatedCount := 0;

        if Opportunity.FindSet() then begin
            repeat
                ProcessedCount += 1;

                if RecalculateOpportunity(Opportunity) then
                    UpdatedCount += 1;

            until Opportunity.Next() = 0;
        end;

        if GuiAllowed() then
            Message(CompleteMsg, ProcessedCount, UpdatedCount);
    end;

    procedure RecalculateFilteredOpportunities()
    var
        Opportunity: Record Opportunity;
        ConfirmQst: Label 'This will recalculate financial fields for filtered opportunities. This process may take some time. Do you want to continue?';
        CompleteMsg: Label 'Recalculation complete. %1 opportunities processed, %2 opportunities updated.';
        ProcessedCount: Integer;
        UpdatedCount: Integer;
        TotalCount: Integer;
    begin
        if not Confirm(ConfirmQst) then
            exit;

        TotalCount := Opportunity.Count();
        ProcessedCount := 0;
        UpdatedCount := 0;

        if Opportunity.FindSet() then begin
            repeat
                ProcessedCount += 1;

                if RecalculateOpportunity(Opportunity) then
                    UpdatedCount += 1;

            until Opportunity.Next() = 0;
        end;

        if GuiAllowed() then
            Message(CompleteMsg, ProcessedCount, UpdatedCount);
    end;

    local procedure RecalculateOpportunity(var Opportunity: Record Opportunity): Boolean
    var
        IsUpdated: Boolean;
        OriginalMarginPercent: Decimal;
        OriginalNetMargin: Decimal;
    begin
        IsUpdated := false;

        // Store original values to check if anything changed
        OriginalMarginPercent := Opportunity."Margin_Percent__c";
        OriginalNetMargin := Opportunity."Net_Margin__c";

        // Recalculate all financial fields
        Opportunity.CalculateMargins();
        Opportunity.CalculateNetMargin();

        // Check if any values changed
        if (Opportunity."Margin_Percent__c" <> OriginalMarginPercent) or
           (Opportunity."Net_Margin__c" <> OriginalNetMargin) then begin

            Opportunity.Modify();
            IsUpdated := true;
        end;

        exit(IsUpdated);
    end;
}