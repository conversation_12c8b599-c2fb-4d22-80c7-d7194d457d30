// FactBox page to show detailed information about a mass email log entry
page 50117 "Mass Email Log Details"
{
    PageType = CardPart;
    ApplicationArea = All;
    Caption = 'Log Entry Details';
    SourceTable = "Mass Email Log";
    Editable = false;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General Information';

                field("Entry No."; Rec."Entry No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Unique entry number for this log record.';
                }
                field("Job ID"; Rec."Job ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'Mass email job identifier.';
                }
                field("Log Date Time"; Rec."Log Date Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Date and time when this log entry was created.';
                }
                field("Log Type"; Rec."Log Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Type of log entry.';
                    StyleExpr = LogTypeStyleExpr;
                }
                field("User ID"; Rec."User ID")
                {
                    ApplicationArea = All;
                    ToolTip = 'User who initiated the mass email job.';
                }
            }

            group(ContactInfo)
            {
                Caption = 'Contact Information';
                Visible = HasContactInfo;

                field("Contact No."; Rec."Contact No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Contact number for email-related log entries.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Contact name for email-related log entries.';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ToolTip = 'Email address for email-related log entries.';
                }
                field("Send Status"; Rec."Send Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Send status for email-related log entries.';
                    StyleExpr = SendStatusStyleExpr;
                }
            }

            group(EmailDetails)
            {
                Caption = 'Email Details';
                Visible = HasEmailInfo;

                field("Email Subject"; Rec."Email Subject")
                {
                    ApplicationArea = All;
                    ToolTip = 'Subject of the email for email-related log entries.';
                    MultiLine = true;
                }
                field("Processing Time (ms)"; Rec."Processing Time (ms)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Time taken to process this email in milliseconds.';
                }
                field("Batch No."; Rec."Batch No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Batch number for email processing.';
                }
                field("Retry Attempt"; Rec."Retry Attempt")
                {
                    ApplicationArea = All;
                    ToolTip = 'Number of retry attempts for failed emails.';
                }
            }

            group(CampaignInfo)
            {
                Caption = 'Campaign Information';
                Visible = HasCampaignInfo;

                field("Campaign No."; Rec."Campaign No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Campaign associated with this email.';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Salesperson associated with this email.';
                }
            }

            group(ErrorInfo)
            {
                Caption = 'Error Information';
                Visible = HasErrorInfo;

                field("Error Message"; Rec."Error Message")
                {
                    ApplicationArea = All;
                    ToolTip = 'Error message for failed operations.';
                    Style = Unfavorable;
                    MultiLine = true;
                }
            }

            group(AdditionalInfo)
            {
                Caption = 'Additional Information';
                Visible = HasAdditionalInfo;

                field("Additional Info"; Rec."Additional Info")
                {
                    ApplicationArea = All;
                    ToolTip = 'Additional information about this log entry.';
                    MultiLine = true;
                }
            }
        }
    }

    var
        LogTypeStyleExpr: Text;
        SendStatusStyleExpr: Text;
        HasContactInfo: Boolean;
        HasEmailInfo: Boolean;
        HasCampaignInfo: Boolean;
        HasErrorInfo: Boolean;
        HasAdditionalInfo: Boolean;

    trigger OnAfterGetRecord()
    begin
        SetStyleExpressions();
        SetGroupVisibility();
    end;

    local procedure SetStyleExpressions()
    begin
        // Set style for Log Type
        case Rec."Log Type" of
            Rec."Log Type"::"Email Sent":
                LogTypeStyleExpr := 'Favorable';
            Rec."Log Type"::"Email Failed":
                LogTypeStyleExpr := 'Unfavorable';
            Rec."Log Type"::"Job Started":
                LogTypeStyleExpr := 'StandardAccent';
            Rec."Log Type"::"Job Completed":
                LogTypeStyleExpr := 'Favorable';
            Rec."Log Type"::"Job Failed":
                LogTypeStyleExpr := 'Unfavorable';
            Rec."Log Type"::"Job Cancelled":
                LogTypeStyleExpr := 'Subordinate';
            Rec."Log Type"::Error:
                LogTypeStyleExpr := 'Unfavorable';
            Rec."Log Type"::Warning:
                LogTypeStyleExpr := 'Attention';
            else
                LogTypeStyleExpr := 'Standard';
        end;

        // Set style for Send Status
        case Rec."Send Status" of
            Rec."Send Status"::Sent:
                SendStatusStyleExpr := 'Favorable';
            Rec."Send Status"::Failed:
                SendStatusStyleExpr := 'Unfavorable';
            Rec."Send Status"::Skipped:
                SendStatusStyleExpr := 'Subordinate';
            else
                SendStatusStyleExpr := 'Standard';
        end;
    end;

    local procedure SetGroupVisibility()
    begin
        HasContactInfo := (Rec."Contact No." <> '') or (Rec."Contact Name" <> '') or (Rec."E-Mail" <> '');
        HasEmailInfo := (Rec."Email Subject" <> '') or (Rec."Processing Time (ms)" > 0) or (Rec."Batch No." > 0) or (Rec."Retry Attempt" > 0);
        HasCampaignInfo := (Rec."Campaign No." <> '') or (Rec."Salesperson Code" <> '');
        HasErrorInfo := Rec."Error Message" <> '';
        HasAdditionalInfo := Rec."Additional Info" <> '';
    end;
}
